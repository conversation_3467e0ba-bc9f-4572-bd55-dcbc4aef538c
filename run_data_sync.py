from datetime import datetime
import os
import shutil

from src.paths import ResearchPath
from src.worker import MyOwnSess
from src.data_sync import DataSync

# 創建會話
sess = MyOwnSess()

# 獲取今天日期
today = datetime.now().strftime("%Y-%m-%d")

# 使用今天日期作為輸出目錄
ds = DataSync(sess, output_dir=today)

# 運行同步
ds.sync_all(simplified=True)

# 複製到 latest
latest_dir = os.path.join(ResearchPath.DATA, "latest")
if os.path.exists(latest_dir):
    shutil.rmtree(latest_dir)
shutil.copytree(ds._root_dir, latest_dir)

print(f"資料同步完成，已儲存至 {today} 並複製到 latest。") 