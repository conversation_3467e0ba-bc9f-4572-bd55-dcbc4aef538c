[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "WQ_open_machine_taiwan"
version = "1.0.0"
requires-python = ">=3.11"
dependencies = [
    "ipykernel>=6.29.5",
    "langchain-google-genai>=2.1.8",
    "matplotlib>=3.7.0",
    "numpy>=1.24.0",
    "ollama>=0.5.1",
    "pandas>=2.3.0",
    "pytelegrambotapi>=4.27.0",
    "python-dotenv>=1.1.1",
    "requests>=2.32.4",
    "seaborn>=0.12.0",
    "tqdm>=4.67.1",
] 

[tool.setuptools]
packages = { find = { where = ["src"] } }
