import os
import requests
from dotenv import load_dotenv

load_dotenv()

TELEGRAM_BOT_API_TOKEN = os.getenv("TELEGRAM_BOT_API_TOKEN")
if __name__ == "__main__":
    try:
        res = requests.get(f'https://api.telegram.org/bot{TELEGRAM_BOT_API_TOKEN}/getUpdates')
        chat_id = res.json()["result"][-1]["message"]["chat"]["id"]
        # 將 chat_id 自動寫入 .env 檔案，變數名稱為 TELEGRAM_CHAT_ID
        with open(".env", "a", encoding="utf-8") as f:
            f.write(f'\nTELEGRAM_CHAT_ID="{chat_id}"\n')
        print("已將 TELEGRAM_CHAT_ID 寫入 .env 檔案")
    except Exception as e:
        print("Error: ", e)