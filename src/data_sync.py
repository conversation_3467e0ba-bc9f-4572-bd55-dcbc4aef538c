"""
資料同步工具
----------------
將下載『模擬設定 → 資料集 → 資料欄位』的流程封裝成易於呼叫與維護的 class 介面。

使用方式 (範例)：

>>> from src.worker import MyOwnSess
>>> from src.data_sync import DataSync
>>> ds = DataSync(MyOwnSess())
>>> ds.sync_all()

結果結構：
ResearchDB/ ── data/ ── latest/
                            ├── all_sim_options.json
                            ├── <region>_<universe>_d<delay>/
                            │   ├── dataset_ids.json
                            │   └── data_fields.json
                            └── ...
"""
from __future__ import annotations

import json
import os
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from itertools import product
from typing import Dict, List

from tqdm import tqdm

import shutil

from paths import ResearchPath
from worker import MyOwnSess


class DataSync:
    """下載 *WorldQuant Brain* 相關設定、資料集、資料欄位，並統一存檔。"""

    # ------------------------  參數設定  ------------------------
    _SIM_API = "https://api.worldquantbrain.com/simulations"
    _DATASETS_API = "https://api.worldquantbrain.com/data-sets"
    _DATAFIELDS_API = "https://api.worldquantbrain.com/data-fields"

    def __init__(self, sess: MyOwnSess, instrument_type: str = "EQUITY", max_workers: int = 8, output_dir: str = "latest") -> None:
        self.sess = sess
        self.instrument_type = instrument_type
        self.max_workers = max_workers

        # 建立輸出資料夾 (使用最新資料夾，若不存在則建立)
        self._root_dir = os.path.join(ResearchPath.DATA, output_dir)
        os.makedirs(self._root_dir, exist_ok=True)

    # ------------------------  對外方法  ------------------------
    def sync_all(self, simplified: bool = False) -> None:
        """整合流程：下載所有模擬設定 → 每組設定抓取資料集 → 個別資料集抓取資料欄位。"""

        options = self._fetch_sim_options()

        if simplified:
            seen_regions = set()
            unique_options = []
            for opt in options:
                if opt['region'] not in seen_regions:
                    unique_options.append(opt)
                    seen_regions.add(opt['region'])
            options = unique_options

        # 確保 latest 資料夾存在
        latest_root = os.path.join(ResearchPath.DATA, "latest")
        os.makedirs(latest_root, exist_ok=True)

        self._save_json(options, os.path.join(self._root_dir, "all_sim_options.json"))
        # 複製到 latest
        shutil.copy(os.path.join(self._root_dir, "all_sim_options.json"), os.path.join(latest_root, "all_sim_options.json"))

        for opt in tqdm(options, desc="各設定下載資料集"):
            key = self._setting_key(opt)
            target_dir = os.path.join(self._root_dir, key)
            os.makedirs(target_dir, exist_ok=True)

            # Step 1. 下載資料集清單
            dataset_ids = self._fetch_datasets(opt)
            self._save_json(dataset_ids, os.path.join(target_dir, "dataset_ids.json"))

            # Step 2. 下載每個資料集的欄位
            data_fields_map = self._fetch_datafields_bulk(dataset_ids, opt)
            id_only_map = {ds_id: [field['id'] for field in fields] for ds_id, fields in data_fields_map.items()}
            self._save_json(id_only_map, os.path.join(target_dir, "data_fields.json"))

            # 額外儲存每個資料集的原始資料欄位
            raw_dir = os.path.join(target_dir, "data_fields_raw")
            os.makedirs(raw_dir, exist_ok=True)
            for ds_id, fields in data_fields_map.items():
                self._save_json(fields, os.path.join(raw_dir, f"{ds_id}.json"))

            # 每處理完一個設定，複製到 latest
            latest_target = os.path.join(latest_root, key)
            if os.path.exists(latest_target):
                shutil.rmtree(latest_target)
            shutil.copytree(target_dir, latest_target)

    def sync_specific(self, opt: Dict) -> None:
        """為特定模擬設定 (region, universe, delay) 下載並更新資料集與欄位。"""

        key = self._setting_key(opt)
        target_dir = os.path.join(self._root_dir, key)
        os.makedirs(target_dir, exist_ok=True)

        # Step 1. 下載資料集清單
        dataset_ids = self._fetch_datasets(opt)
        self._save_json(dataset_ids, os.path.join(target_dir, "dataset_ids.json"))

        # Step 2. 下載每個資料集的欄位
        data_fields_map = self._fetch_datafields_bulk(dataset_ids, opt)
        id_only_map = {ds_id: [field['id'] for field in fields] for ds_id, fields in data_fields_map.items()}
        self._save_json(id_only_map, os.path.join(target_dir, "data_fields.json"))

        # 額外儲存每個資料集的原始資料欄位
        raw_dir = os.path.join(target_dir, "data_fields_raw")
        os.makedirs(raw_dir, exist_ok=True)
        for ds_id, fields in data_fields_map.items():
            self._save_json(fields, os.path.join(raw_dir, f"{ds_id}.json"))

        # 複製到 latest
        latest_root = os.path.join(ResearchPath.DATA, "latest")
        os.makedirs(latest_root, exist_ok=True)
        latest_target = os.path.join(latest_root, key)
        if os.path.exists(latest_target):
            shutil.rmtree(latest_target)
        shutil.copytree(target_dir, latest_target)

    # ----------------------  內部輔助方法  ----------------------
    # 1. 模擬設定
    def _fetch_sim_options(self) -> List[Dict]:
        """取得所有 (region, universe, delay) 的可能組合。"""
        res = self.sess.options(self._SIM_API)
        settings = res.json()["actions"]["POST"]["settings"]["children"]

        region_choices = settings["region"]["choices"]["instrumentType"][self.instrument_type]
        universe_choices = settings["universe"]["choices"]["instrumentType"][self.instrument_type]
        delay_choices = settings["delay"]["choices"]["instrumentType"][self.instrument_type]

        regions = [c["value"] for c in region_choices]

        all_options: List[Dict] = []
        for region in regions:
            universes = [c["value"] for c in universe_choices["region"][region]]
            delays = [c["value"] for c in delay_choices["region"][region]]
            all_options.extend(
                {"region": region, "universe": u, "delay": d} for u, d in product(universes, delays)
            )
        return all_options

    # 2. 資料集
    def _fetch_datasets(self, opt: Dict) -> List[str]:
        payload = {
            "delay": opt["delay"],
            "instrumentType": self.instrument_type,
            "limit": 50,
            "offset": 0,
            "region": opt["region"],
            "universe": opt["universe"],
        }

        first_page = self.sess.get(self._DATASETS_API, params=payload).json()
        count = first_page["count"]
        results = first_page["results"]

        # 若回傳量 > 50，分頁下載
        total_pages = (count - 1) // 50 + 1  # 計算總頁數
        with tqdm(total=total_pages, desc=f"下載 {opt['region']}_{opt['universe']}_d{opt['delay']} 資料集", leave=False) as pbar:
            pbar.update(1)  # 第一頁已下載
            for offset in range(50, count, 50):
                payload["offset"] = offset
                page = self.sess.get(self._DATASETS_API, params=payload).json()
                results.extend(page["results"])
                time.sleep(1)  # 避免 hit rate limit
                pbar.update(1)

        return [ds["id"] for ds in results]

    # 3. 資料欄位 (單一)
    def _fetch_datafields(self, dataset_id: str, opt: Dict) -> List[Dict]:
        payload = {
            "dataset.id": dataset_id,
            "delay": opt["delay"],
            "region": opt["region"],
            "universe": opt["universe"],
            "instrumentType": self.instrument_type,
            "limit": 50,
            "offset": 0,
        }

        first_page = self.sess.get(self._DATAFIELDS_API, params=payload).json()
        count = first_page["count"]
        results = first_page["results"]

        total_pages = (count - 1) // 50 + 1
        with tqdm(total=total_pages, desc=f"下載 {dataset_id} 資料欄位", leave=False) as pbar:
            pbar.update(1)
            for offset in range(50, count, 50):
                payload["offset"] = offset
                page = self.sess.get(self._DATAFIELDS_API, params=payload).json()
                results.extend(page["results"])
                time.sleep(1)
                pbar.update(1)

        return results

    # 3-1. 多筆資料欄位 (thread pool)
    def _fetch_datafields_bulk(self, dataset_ids: List[str], opt: Dict) -> Dict[str, List[Dict]]:
        ans: Dict[str, List[Dict]] = {}
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {
                executor.submit(self._fetch_datafields, ds_id, opt): ds_id for ds_id in dataset_ids
            }
            for future in tqdm(as_completed(futures), total=len(futures), desc="資料欄位下載", leave=False):
                ds_id = futures[future]
                try:
                    ans[ds_id] = future.result()
                except Exception as e:
                    ans[ds_id] = []
                    print(f"資料集 {ds_id} 下載失敗: {e}")
        return ans

    # utilities
    @staticmethod
    def _save_json(obj, path: str) -> None:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(obj, f, ensure_ascii=False, indent=2)

    @staticmethod
    def _setting_key(opt: Dict) -> str:
        return f"{opt['region']}_{opt['universe']}_d{opt['delay']}" 