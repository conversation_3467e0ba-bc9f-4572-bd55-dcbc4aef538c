from hashlib import sha256

from custom_op_definition import MAPPING


def _encoding_string(string: str):
    return "c_" + str(int(sha256(string.encode("utf-8")).hexdigest(), 16) % (10 ** 8))


def _detect_money_symbol_in_lines(line_expr: list[str]) -> bool:
    return any(["$" in line for line in line_expr])


def _find_outermost_parentheses_and_comma(expr: str) -> list[int]:
    stack = []
    parentheses_and_comma_place = []
    for i, char in enumerate(expr):
        if char == '(':
            if len(stack) == 0:
                parentheses_and_comma_place.append(i)
            stack.append(i)
        if char == "," and len(stack) == 1:
            parentheses_and_comma_place.append(i)
        elif char == ')':
            if len(stack) != 0:
                stack.pop()
                if len(stack) == 0:
                    parentheses_and_comma_place.append(i)
                    return parentheses_and_comma_place
    return None


def _split_by_first_money_symbol(line: str) -> tuple[str, str]:
    for i, char in enumerate(line):
        if char == "$":
            return line[:i], line[i + 1:]


def _get_custom_op_parameter(line: str, place_of_seperation: list[int]) -> list[str]:
    return_list = []
    length = len(place_of_seperation)
    for i in range(length - 1):
        return_list.append(line[place_of_seperation[i] + 1:place_of_seperation[i + 1]])
    return return_list


def _split_lines(line: str) -> tuple[str, str, str, list[int]]:
    first_part, money_symbol_and_last_part = _split_by_first_money_symbol(line)
    seperation_place = _find_outermost_parentheses_and_comma(money_symbol_and_last_part)
    money_symbol_part = money_symbol_and_last_part[:(seperation_place[-1]) + 1]
    last_part = money_symbol_and_last_part[(seperation_place[-1] + 1):]
    return first_part, money_symbol_part, last_part, seperation_place


def replace_custom_op(expr: str) -> str:
    line_expr = expr.replace("\n", "").replace(" ", "").split(";")
    copy_expr = []
    replace_dict = {}
    while _detect_money_symbol_in_lines(line_expr):
        for line in line_expr:
            for key in replace_dict:
                line = line.replace(key, replace_dict[key])
            if "$" in line:
                first_part, money_symbol_part, last_part, seperation_place = _split_lines(line)
                hashed_string = _encoding_string(money_symbol_part)
                replace_dict[f"${money_symbol_part}"] = hashed_string
                string_replaced_custom = MAPPING[money_symbol_part[:seperation_place[0]]](*_get_custom_op_parameter(money_symbol_part, seperation_place))

                copy_expr.append("=".join([hashed_string, string_replaced_custom]))
                copy_expr.append("".join([first_part, hashed_string, last_part]))
            else:
                copy_expr.append(line)
        line_expr = copy_expr.copy()
        copy_expr = []
    expr = ";\n".join(line_expr).replace(" ", "")
    expr = expr.replace(",", ", ")
    expr = expr.replace("+", " + ")
    expr = expr.replace("-", " - ")
    expr = expr.replace("*", " * ")
    expr = expr.replace("/", " / ")
    expr = expr.replace("=", " = ")
    expr = expr.replace("^", " ^ ")
    return expr
