import json
import random
import os
import shutil
import typing

from alpha import Alpha
from alphalist import AlphaList, bcorr_filter
from scorer import AlphaScore
from alpha_utils import percentile
from paths import ResearchPath
# 設置參數處理功能（整合自 setting_utils）
import copy


def parse_setting_file(setting_data: dict) -> tuple[dict, dict]:
    """
    解析設置檔案，區分固定值和可變值

    Args:
        setting_data: 設置檔案的內容

    Returns:
        tuple: (base_settings, setting_pool)
        - base_settings: 固定的設置參數
        - setting_pool: 可變的設置參數（空字典表示沒有可變參數）
    """
    base_settings = {}
    setting_pool = {}

    for key, value in setting_data.items():
        if isinstance(value, list):
            # 如果是 list，加入到 setting_pool
            setting_pool[f"<{key}>"] = value
            # base_settings 中使用 placeholder
            base_settings[key] = f"<{key}>"
        else:
            # 如果是固定值，直接加入到 base_settings
            base_settings[key] = value

    return base_settings, setting_pool


def generate_setting_from_gene(base_settings: dict, setting_gene: dict) -> dict:
    """
    根據設置基因生成完整的設置檔案

    Args:
        base_settings: 基礎設置模板
        setting_gene: 設置基因（包含選擇的值）

    Returns:
        完整的設置檔案
    """
    settings = copy.deepcopy(base_settings)

    for key, value in settings.items():
        if isinstance(value, str) and value.startswith("<") and value.endswith(">"):
            # 如果是 placeholder，替換為對應的基因值
            if value in setting_gene:
                settings[key] = setting_gene[value]

    return settings


def get_setting_diversity_stats(settings_list: list) -> dict:
    """
    計算設置參數的多樣性統計

    Args:
        settings_list: 設置列表

    Returns:
        多樣性統計資料
    """
    if not settings_list:
        return {}

    stats = {}

    # 獲取所有可能的 key
    all_keys = set()
    for settings in settings_list:
        all_keys.update(settings.keys())

    for key in all_keys:
        values = []
        for settings in settings_list:
            if key in settings:
                values.append(settings[key])

        unique_values = list(set(str(v) for v in values))
        stats[key] = {
            "unique_count": len(unique_values),
            "unique_values": unique_values[:10],  # 最多顯示 10 個不同值
            "total_count": len(values)
        }

    return stats


class GenerationAlgorithm:
    def __init__(self,
                 gene_pool: dict,
                 payload: dict,
                 template: str,
                 main_scorer: AlphaScore,
                 addition_scorer: list[AlphaScore],
                 research_name: str,
                 ga_config: dict[str: int | float] = {
                            "parent_num": 10,
                            "kid_num": 40,
                            "generation_num": 10,
                            "mutation_rate": 0.5,
                            "early_stop_patience": 3},
                 setting_data: dict = None) -> None:

        # variables that you gave for initiation
        # ga config
        self.g_num = ga_config["generation_num"]
        self.mutation_rate = ga_config["mutation_rate"]
        self.parent_num = ga_config["parent_num"]
        self.kid_num = ga_config["kid_num"]
        self.bcorr_threshold = ga_config["bcorr_threshold"]

        # research name
        ResearchPath.ensure_dirs()
        self.research_folder = os.path.join(ResearchPath.RESEARCH_PROCESS, research_name)

        # other variance
        self.gene_pool = gene_pool
        self.payload = payload
        self.template = template

        # about scoring
        self.main_score = main_scorer
        self.addition_score = addition_scorer
        # variable that you should not call
        self.parent = []
        self.alpha_to_genome = {}  # 改名為 genome，同時儲存基因和設置

        # Early Stop 功能的變數
        self.generation_scores = []  # 記錄每一代的平均分數
        self.early_stop_patience = ga_config.get("early_stop_patience", 3)  # 連續幾代沒進步就停止

        self.base_settings, self.setting_pool = parse_setting_file(setting_data)
        print(f"設置參數優化: {len(self.setting_pool)} 個可變參數")

        # 上面 ensure_dirs 已建立 RESEARCH_PROCESS

    def run(self, auto_run: bool = False, exist_ok: bool = False) -> None:
        # auto run if not print a expr for debug
        if not auto_run:
            print("Please check whether the following expr is appropriate")
            print("------------------------------------------------------")
            sample_gene, sample_setting_gene = self.cross_over_genome(None)
            sample_settings = generate_setting_from_gene(self.base_settings, sample_setting_gene)
            print(f"表達式: {self.get_expr(sample_gene)}")
            print(f"設置參數: {sample_settings}")

            while True:
                yes_or_no = input("Y for continue, N for return. Y/N:").upper()
                if yes_or_no == "Y":
                    break
                elif yes_or_no == "N":
                    return
                print("Wrong Character")
        print("\n")
        # check whether the research process exist
        if not exist_ok and os.path.exists(self.research_folder):
            print("This research process already exists. Do you want to keep it?")
            while True:
                yes_or_no = input("Y for keep the previous one, N for delete the previous one. Y/N:").upper()
                if yes_or_no == "Y":
                    break
                elif yes_or_no == "N":
                    shutil.rmtree(self.research_folder)
                    break
                print("Wrong character")
        os.makedirs(self.research_folder, exist_ok=True)
        os.makedirs(f"{self.research_folder}/parent_and_variance", exist_ok=True)
        self.save(f"{self.research_folder}/ga_config")

        # load check point
        check_point = self.load_check_point().get("g", 0)

        # 載入之前的分數記錄（如果有的話）
        if "generation_scores" in self.load_check_point():
            self.generation_scores = self.load_check_point()["generation_scores"]

        for g in range(1, self.g_num + 1, 1):
            # for check point
            if g < check_point:
                continue
            elif g == check_point:
                genome_path = f"{self.research_folder}/parent_and_variance/alpha_to_genome"
                self.alpha_to_genome = self.load_alpha_to_genome(genome_path)
                self.parent = self.load_parentname(f"{self.research_folder}/parent_and_variance/parent_{g}")
                continue

            # save check point
            self.generate_next_generation(g)
            self.out_put_generation_score(g)

            # 計算並記錄當前世代的平均分數
            avg_score = self.calculate_generation_avg_score(g)
            self.generation_scores.append(avg_score)
            print(f"第 {g} 代平均分數: {avg_score:.6f}")

            self.selection(g)

            self.save_parentname(f"{self.research_folder}/parent_and_variance/parent_{g}")
            self.save_alpha_to_genome(f"{self.research_folder}/parent_and_variance/alpha_to_genome")
            self.save_check_point({"g": g, "generation_scores": self.generation_scores})

            # Early Stop 檢查
            if self.check_early_stop():
                print(f"\n提前停止訓練：連續 {self.early_stop_patience} 代沒有進步")
                print(f"最近 {self.early_stop_patience} 代的平均分數: {self.generation_scores[-self.early_stop_patience:]}")
                break

    def selection(self, g: int) -> None:
        self.parent = []
        alphalist_orig = AlphaList.load(f"{self.research_folder}/alphalist_{g}")

        # 1. 過濾掉分數為 None 的無效 alpha
        valid_alphas = AlphaList()
        valid_alphas.alpha_list = [
            alpha for alpha in alphalist_orig.alpha_list if self.main_score.score(alpha) is not None
        ]

        if not valid_alphas.alpha_list:
            print(f"警告: 第 {g} 代沒有任何有效的 Alpha。跳過選擇階段。")
            self.parent = []
            return

        # 2. 進行相關性過濾
        alphalist = bcorr_filter(valid_alphas, self.main_score, corr_threshold=self.bcorr_threshold)
        if not alphalist.alpha_list:
            print(f"警告: 第 {g} 代的 Alpha 經過相關性過濾後全部被移除。跳過選擇階段。")
            self.parent = []
            return

        # 3. 計算分數並選出父母
        all_alpha_score = [self.main_score.score(alpha) for alpha in alphalist.alpha_list]

        # all_alpha_score 不會是空的，因為 alphalist.alpha_list 不是空的
        score_ther = percentile(all_alpha_score, 1 - self.parent_num / self.kid_num)

        for i in range(len(alphalist.alpha_list)):
            if all_alpha_score[i] >= score_ther:
                self.parent.append(alphalist.alpha_list[i].name)

    def generate_next_generation(self, g: int) -> None:
        alphalist = AlphaList()
        for num in range(self.kid_num):
            # about getting alpha var
            if g == 1:
                parent_genome = None
            else:
                parent_name = random.sample(self.parent, 2)
                parent_genome = [self.alpha_to_genome[parent_name[0]], self.alpha_to_genome[parent_name[1]]]

            # 交配和突變
            gene, setting_gene = self.cross_over_genome(parent_genome)
            gene, setting_gene = self.mutation_genome(gene, setting_gene)

            # 生成 alpha
            payload = self.payload.copy()
            payload["regular"] = self.get_expr(gene)

            # 生成設置參數
            final_settings = generate_setting_from_gene(self.base_settings, setting_gene)
            payload["settings"] = final_settings


            alpha = Alpha(payload=payload)
            alpha.set_name()

            # 儲存完整的基因組資料
            self.alpha_to_genome[alpha.name] = {
                "gene": gene,
                "setting_gene": setting_gene
            }

            alphalist.append(alpha)
        alphalist.save(f"{self.research_folder}/alphalist_{g}")
        alphalist.sim_all()

    def cross_over_genome(self, parent_genome: list[dict] | None) -> tuple[dict, dict]:
        """
        新的交配方法，同時處理基因和設置基因

        Returns:
            tuple: (gene, setting_gene)
        """
        gene = {}
        setting_gene = {}

        # 處理表達式基因
        for key in self.gene_pool.keys():
            if parent_genome is None:
                gene[key] = random.choice(self.gene_pool[key])
            else:
                gene[key] = random.choice([parent_genome[0]["gene"][key], parent_genome[1]["gene"][key]])

        # 處理設置基因
        for key in self.setting_pool.keys():
            if parent_genome is None:
                setting_gene[key] = random.choice(self.setting_pool[key])
            else:
                setting_gene[key] = random.choice([
                    parent_genome[0]["setting_gene"].get(key, random.choice(self.setting_pool[key])),
                    parent_genome[1]["setting_gene"].get(key, random.choice(self.setting_pool[key]))
                ])

        return gene, setting_gene

    def mutation_genome(self, gene: dict[str: str], setting_gene: dict[str: str]) -> tuple[dict, dict]:
        """
        新的突變方法，同時處理基因和設置基因

        Returns:
            tuple: (mutated_gene, mutated_setting_gene)
        """
        # 突變表達式基因
        for key in gene.keys():
            if random.random() <= self.mutation_rate:
                gene[key] = random.choice(self.gene_pool[key])

        # 突變設置基因
        for key in setting_gene.keys():
            if random.random() <= self.mutation_rate:
                setting_gene[key] = random.choice(self.setting_pool[key])

        return gene, setting_gene

    def get_expr(self, gene: dict[str: str]) -> str:
        expr = self.template
        for keys in gene.keys():
            expr = expr.replace(keys, str(gene[keys]))
        return expr

    def out_put_generation_score(self, g: int) -> None:
        print(f"-----{g} generation score-----")
        alphalist = AlphaList.load(f"{self.research_folder}/alphalist_{g}")

        # 計算主要分數
        main_scores = [self.main_score.score(alpha) for alpha in alphalist.alpha_list]
        main_scores = [s for s in main_scores if s is not None]  # 過濾掉 None

        if main_scores:
            main_percentile = percentile(main_scores, 0.75)
            print(f"Main score: {self.main_score.name()} {main_percentile}")
        else:
            print(f"Main score: {self.main_score.name()} N/A (無有效分數)")

        # 計算額外分數
        if self.addition_score:
            for scorer in self.addition_score:
                additional_scores = [scorer.score(alpha) for alpha in alphalist.alpha_list]
                additional_scores = [s for s in additional_scores if s is not None]  # 過濾掉 None

                if additional_scores:
                    additional_percentile = percentile(additional_scores, 0.75)
                    print(f"Additional score: {scorer.name()} {additional_percentile}")
                else:
                    print(f"Additional score: {scorer.name()} N/A (無有效分數)")
        print("------------------------------")

    def calculate_generation_avg_score(self, g: int) -> float:
        """計算當前世代的平均分數"""
        alphalist = AlphaList.load(f"{self.research_folder}/alphalist_{g}")
        scores = [self.main_score.score(alpha) for alpha in alphalist.alpha_list]
        scores = [s for s in scores if s is not None]  # 過濾掉 None

        if not scores:
            return None  # 如果沒有有效分數，返回 None

        return sum(scores) / len(scores)

    def check_early_stop(self) -> bool:
        """檢查是否需要提前停止訓練"""
        if len(self.generation_scores) < self.early_stop_patience:
            return False

        # 檢查最近三代的分數是否都沒有進步
        recent_scores = self.generation_scores[-self.early_stop_patience:]

        # 如果最近的分數都比前一個分數小或相等，表示沒有進步
        no_improvement = True
        for i in range(1, len(recent_scores)):
            if recent_scores[i] > recent_scores[i-1]:
                no_improvement = False
                break

        return no_improvement

    def save_check_point(self, check_point: dict[str: int]) -> None:
        f = open(f"{self.research_folder}/check_point", 'w')
        json.dump(check_point, f, indent=2)
        f.close()

    def load_check_point(self) -> dict:
        if not os.path.exists(f"{self.research_folder}/check_point"):
            return {}
        f = open(f"{self.research_folder}/check_point", 'r')
        check_point = json.load(f)
        f.close()
        return check_point

    def save_parentname(self, path: str) -> None:
        with open(path,"w") as f:
            json.dump(self.parent, f, indent=2)

    @classmethod
    def load_parentname(cls, path: str) -> list[str]:
        # return parent name from a given path
        with open(path,'r') as f:
            names=json.load(f)
        return names

    def save_alpha_to_genome(self, path: str) -> None:
        """儲存完整的基因組資料（包括基因和設置基因）"""
        f = open(path, 'w')
        json.dump(self.alpha_to_genome, f, indent=2)
        f.close()

    @classmethod
    def load_alpha_to_genome(cls, path: str) -> dict[str: dict]:
        """載入完整的基因組資料"""
        h = open(path, 'r')
        alpha_to_genome = json.load(h)
        h.close()
        return alpha_to_genome

    def save(self, path: str) -> None:
        f = open(path, 'w')
        save_data = {
            "payload": self.payload,
            "scorer": self.main_score.name(),
            "gene_pool": self.gene_pool,
            "template": self.template,
            "ga_config": {
                "parent_num": self.parent_num,
                "kid_num": self.kid_num,
                "g_num": self.g_num,
                "mutation_rate": self.mutation_rate,
                "early_stop_patience": self.early_stop_patience,
                "bcorr_threshold": self.bcorr_threshold,
            },
        }

        save_data["base_settings"] = self.base_settings
        save_data["setting_pool"] = self.setting_pool

        json.dump(save_data, f, indent=2)
        f.close()

    @classmethod
    def load(cls, path: str) -> typing.Self:
        f = open(path, 'r')
        ret_dict = json.load(f)
        f.close()

        print("Note that this ga doesn't have additional alpha score research_name set them to use ga.run()")

        setting_data = None

        base_settings = ret_dict.get("base_settings", {})
        setting_pool = ret_dict.get("setting_pool", {})

        # 將設置重組為原始格式
        setting_data = base_settings.copy()
        for placeholder, options in setting_pool.items():
            original_key = placeholder.strip('<>')
            setting_data[original_key] = options

        return cls(ret_dict["gene_pool"],
                   ret_dict["payload"],
                   ret_dict["template"],
                   ret_dict["scorer"],  # 修正拼寫錯誤
                   None,
                   None,
                   ga_config=ret_dict["ga_config"],
                   setting_data=setting_data
                   )
