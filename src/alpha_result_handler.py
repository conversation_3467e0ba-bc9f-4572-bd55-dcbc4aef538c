import pandas as pd
import os

from alpha import Alpha
from alphalist import AlphaList
from scorer import AlphaScore
from paths import ResearchPath


class AlphaResultHandler:
    def __init__(self, reaserch_name: str, scorers: list[AlphaScore]):
        self.scorers = scorers
        self.research_name = reaserch_name
        self.research_folder = ResearchPath.research_folder(reaserch_name)
        self.alpha_result = self._load_alpha_result()
        self.alpha_group_result = self._load_group_alpha_result()
        pass

    def _load_alpha_result(self) -> pd.DataFrame:
        dataframes = []
        g = 1
        while True:
            if os.path.exists(os.path.join(self.research_folder, f"alphalist_{g}")):
                dataframes.append(self._load_generation_result(g))
                g += 1
            else:
                break
        return pd.concat(dataframes)

    def _load_group_alpha_result(self) -> pd.DataFrame:
        score_names = [scorer.name() for scorer in self.scorers]
        score_names.append("g")
        grouped_result = self.alpha_result[score_names].groupby("g")
        return grouped_result.quantile(0.75)

    def _load_generation_result(self, g: int) -> pd.DataFrame:
        alphalist = AlphaList.load(os.path.join(self.research_folder, f"alphalist_{g}"))
        columns = ["id", "pass_or_not","name"]
        data = [[alpha["id"], self._check_pass_or_not(alpha),alpha.name] for alpha in alphalist.alpha_list]
        data = pd.DataFrame(data, columns=columns)

        data["g"] = g
        for scorer in self.scorers:
            score = [scorer.score(alpha) for alpha in alphalist.alpha_list]
            data[scorer.name()] = score
        return data

    def _check_pass_or_not(self, alpha: Alpha) -> bool:
        pass_or_not = True
        for check in alpha.result["is"]["checks"]:
            if check["result"] == "FAIL":
                pass_or_not = False
                break
        return pass_or_not
