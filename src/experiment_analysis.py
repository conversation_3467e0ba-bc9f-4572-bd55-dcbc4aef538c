import json
import os
import pickle
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional
from collections import Counter

from alpha import Alpha
from alphalist import AlphaList, bcorr_filter
from scorer import FitnessScore
from paths import ResearchPath
from generation_algorithm import get_setting_diversity_stats


class ExperimentAnalysis:
    """實驗結果分析器"""
    
    def __init__(self, research_name: str):
        """
        初始化實驗分析器
        
        Args:
            research_name: 研究名稱
        """
        self.research_name = research_name
        self.research_folder = os.path.join(ResearchPath.RESEARCH_PROCESS, research_name)
        self.analysis_folder = os.path.join(self.research_folder, "analysis")
        
        # 創建分析資料夾
        os.makedirs(self.analysis_folder, exist_ok=True)
        
        # 載入研究配置
        self.ga_config = self._load_ga_config()
        self.alpha_to_genome = self._load_alpha_to_genome()
        self.alpha_to_gene = self._load_alpha_to_gene()  # 保持向後兼容
        
        # 設置圖表樣式
        plt.rcParams['font.size'] = 12
        plt.rcParams['axes.labelsize'] = 14
        plt.rcParams['axes.titlesize'] = 16
        plt.rcParams['legend.fontsize'] = 12
        
    def _load_ga_config(self) -> Dict:
        """載入 GA 配置"""
        config_path = os.path.join(self.research_folder, "ga_config")
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                return json.load(f)
        return {}
    
    def _load_alpha_to_genome(self) -> Dict:
        """載入 alpha 到基因組的映射（新格式）"""
        genome_path = os.path.join(self.research_folder, "parent_and_variance", "alpha_to_genome")
        if os.path.exists(genome_path):
            with open(genome_path, 'r') as f:
                return json.load(f)
        return {}

    def _load_alpha_to_gene(self) -> Dict:
        """載入 alpha 到基因的映射（舊格式，保持向後兼容）"""
        gene_path = os.path.join(self.research_folder, "parent_and_variance", "alpha_to_gene")
        if os.path.exists(gene_path):
            with open(gene_path, 'r') as f:
                return json.load(f)
        
        # 如果沒有舊格式但有新格式，從新格式中提取
        if self.alpha_to_genome:
            alpha_to_gene = {}
            for alpha_name, genome in self.alpha_to_genome.items():
                alpha_to_gene[alpha_name] = genome.get("gene", {})
            return alpha_to_gene
        
        return {}
    
    def _get_generation_count(self) -> int:
        """獲取世代數量"""
        g = 1
        while os.path.exists(os.path.join(self.research_folder, f"alphalist_{g}")):
            g += 1
        return g - 1
    
    def analyze_generation_score_evolution(self) -> pd.DataFrame:
        """
        分析世代分數演化
        
        Returns:
            包含每代分數統計的 DataFrame
        """
        print("正在進行世代分數演化分析...")
        
        generation_count = self._get_generation_count()
        generations_data = []
        
        for g in range(1, generation_count + 1):
            alphalist = AlphaList.load(os.path.join(self.research_folder, f"alphalist_{g}"))
            
            # 計算各種分數統計
            fitness_scores = []
            sharpe_scores = []
            turnover_scores = []
            
            for alpha in alphalist.alpha_list:
                try:
                    # 對可能造成Nonetype的alpha，跳過計算
                    fitness_score = FitnessScore.score(alpha)
                    sharpe_score = alpha.get_sharpe()
                    turnover_score = alpha.get_turnover()
                    if fitness_score is None or sharpe_score is None or turnover_score is None:
                        continue
                    
                    fitness_scores.append(fitness_score)
                    sharpe_scores.append(sharpe_score)
                    turnover_scores.append(turnover_score)
                except:
                    continue
            
            if fitness_scores:  # 確保有有效分數
                generation_data = {
                    'generation': g,
                    'fitness_mean': np.nanmean(fitness_scores),
                    'fitness_std': np.nanstd(fitness_scores),
                    'fitness_max': np.nanmax(fitness_scores),
                    'fitness_min': np.nanmin(fitness_scores),
                    'fitness_q75': np.nanpercentile(fitness_scores, 75),
                    'fitness_q25': np.nanpercentile(fitness_scores, 25),
                    'sharpe_mean': np.nanmean(sharpe_scores),
                    'sharpe_std': np.nanstd(sharpe_scores),
                    'sharpe_max': np.nanmax(sharpe_scores),
                    'turnover_mean': np.nanmean(turnover_scores),
                    'alpha_count': len(fitness_scores)
                }
                generations_data.append(generation_data)
        
        df = pd.DataFrame(generations_data)
        
        # 視覺化世代分數演化
        self._plot_generation_score_evolution(df)
        
        # 保存結果
        df.to_csv(os.path.join(self.analysis_folder, "generation_score_evolution.csv"), index=False)
        
        return df
    
    def _plot_generation_score_evolution(self, df: pd.DataFrame):
        """繪製世代分數演化圖"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'Generation Score Evolution - {self.research_name}', fontsize=16)
        
        # Fitness 分數演化
        axes[0, 0].plot(df['generation'], df['fitness_mean'], 'b-', label='Mean', linewidth=2)
        axes[0, 0].fill_between(df['generation'], 
                                df['fitness_mean'] - df['fitness_std'],
                                df['fitness_mean'] + df['fitness_std'],
                                alpha=0.3, label='±1 STD')
        axes[0, 0].plot(df['generation'], df['fitness_max'], 'g--', label='Max')
        axes[0, 0].plot(df['generation'], df['fitness_q75'], 'r:', label='Q75')
        axes[0, 0].set_title('Fitness Score Evolution')
        axes[0, 0].set_xlabel('Generation')
        axes[0, 0].set_ylabel('Fitness Score')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # Sharpe 分數演化
        axes[0, 1].plot(df['generation'], df['sharpe_mean'], 'b-', label='Mean', linewidth=2)
        axes[0, 1].plot(df['generation'], df['sharpe_max'], 'g--', label='Max')
        axes[0, 1].set_title('Sharpe Ratio Evolution')
        axes[0, 1].set_xlabel('Generation')
        axes[0, 1].set_ylabel('Sharpe Ratio')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # Turnover 演化
        axes[1, 0].plot(df['generation'], df['turnover_mean'], 'purple', linewidth=2)
        axes[1, 0].set_title('Turnover Evolution')
        axes[1, 0].set_xlabel('Generation')
        axes[1, 0].set_ylabel('Average Turnover')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Alpha 數量演化
        axes[1, 1].bar(df['generation'], df['alpha_count'], alpha=0.7)
        axes[1, 1].set_title('Alpha Count per Generation')
        axes[1, 1].set_xlabel('Generation')
        axes[1, 1].set_ylabel('Number of Valid Alphas')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.analysis_folder, "generation_score_evolution.png"), 
                   dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 世代分數演化圖已保存到: {self.analysis_folder}/generation_score_evolution.png")
    
    def analyze_genetic_diversity(self) -> pd.DataFrame:
        """
        分析世代基因多樣性
        
        Returns:
            包含每代基因多樣性統計的 DataFrame
        """
        print("正在進行基因多樣性分析...")
        
        generation_count = self._get_generation_count()
        diversity_data = []
        
        for g in range(1, generation_count + 1):
            alphalist = AlphaList.load(os.path.join(self.research_folder, f"alphalist_{g}"))
            
            # 收集該世代所有基因
            generation_genes = []
            for alpha in alphalist.alpha_list:
                if alpha.name in self.alpha_to_gene:
                    generation_genes.append(self.alpha_to_gene[alpha.name])
            
            if not generation_genes:
                continue
            
            # 計算基因多樣性指標
            diversity_metrics = self._calculate_genetic_diversity(generation_genes, g)
            diversity_data.append(diversity_metrics)
        
        df = pd.DataFrame(diversity_data)
        
        # 視覺化基因多樣性
        self._plot_genetic_diversity(df)
        
        # 保存結果
        df.to_csv(os.path.join(self.analysis_folder, "genetic_diversity.csv"), index=False)
        
        return df
    
    def _calculate_genetic_diversity(self, genes: List[Dict], generation: int) -> Dict:
        """計算基因多樣性指標"""
        
        # 獲取所有基因鍵
        if not genes:
            return {'generation': generation}
        
        gene_keys = list(genes[0].keys())
        diversity_metrics = {'generation': generation}
        
        # 計算每個基因位點的多樣性
        total_entropy = 0
        total_unique_ratio = 0
        
        for key in gene_keys:
            values = [gene[key] for gene in genes]
            
            # 計算熵（Shannon Entropy）
            value_counts = Counter(values)
            total_count = len(values)
            entropy = 0
            for count in value_counts.values():
                p = count / total_count
                if p > 0:
                    entropy -= p * np.log2(p)
            
            # 計算唯一值比例
            unique_ratio = len(value_counts) / total_count
            
            diversity_metrics[f'{key}_entropy'] = entropy
            diversity_metrics[f'{key}_unique_ratio'] = unique_ratio
            
            total_entropy += entropy
            total_unique_ratio += unique_ratio
        
        # 計算總體多樣性指標
        diversity_metrics['average_entropy'] = total_entropy / len(gene_keys)
        diversity_metrics['average_unique_ratio'] = total_unique_ratio / len(gene_keys)
        diversity_metrics['gene_count'] = len(genes)
        
        return diversity_metrics
    
    def _plot_genetic_diversity(self, df: pd.DataFrame):
        """繪製基因多樣性圖"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'Genetic Diversity Analysis - {self.research_name}', fontsize=16)
        
        # 平均熵演化
        axes[0, 0].plot(df['generation'], df['average_entropy'], 'b-', linewidth=2, marker='o')
        axes[0, 0].set_title('Average Genetic Entropy Evolution')
        axes[0, 0].set_xlabel('Generation')
        axes[0, 0].set_ylabel('Average Entropy')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 平均唯一值比例演化
        axes[0, 1].plot(df['generation'], df['average_unique_ratio'], 'r-', linewidth=2, marker='s')
        axes[0, 1].set_title('Average Unique Ratio Evolution')
        axes[0, 1].set_xlabel('Generation')
        axes[0, 1].set_ylabel('Average Unique Ratio')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 基因數量演化
        axes[1, 0].bar(df['generation'], df['gene_count'], alpha=0.7, color='green')
        axes[1, 0].set_title('Gene Count per Generation')
        axes[1, 0].set_xlabel('Generation')
        axes[1, 0].set_ylabel('Number of Genes')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 多樣性熱圖（如果有足夠的基因位點）
        entropy_cols = [col for col in df.columns if col.endswith('_entropy') and not col.startswith('average')]
        if entropy_cols and len(entropy_cols) > 1:
            entropy_data = df[['generation'] + entropy_cols].set_index('generation')
            entropy_data.columns = [col.replace('_entropy', '') for col in entropy_data.columns]
            
            sns.heatmap(entropy_data.T, ax=axes[1, 1], cmap='viridis', cbar=True)
            axes[1, 1].set_title('Entropy Heatmap by Gene Position')
            axes[1, 1].set_xlabel('Generation')
            axes[1, 1].set_ylabel('Gene Position')
        else:
            axes[1, 1].text(0.5, 0.5, 'Insufficient gene diversity\nfor heatmap visualization', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Gene Diversity Heatmap')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.analysis_folder, "genetic_diversity.png"), 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    def analyze_setting_diversity(self) -> pd.DataFrame:
        """
        分析設置參數的多樣性
        
        Returns:
            包含設置參數多樣性統計的 DataFrame
        """
        print("正在進行設置參數多樣性分析...")
        
        if not self.alpha_to_genome:
            print("警告：沒有找到設置參數優化資料，跳過設置多樣性分析")
            return pd.DataFrame()
        
        generation_count = self._get_generation_count()
        diversity_data = []
        
        for g in range(1, generation_count + 1):
            alphalist = AlphaList.load(os.path.join(self.research_folder, f"alphalist_{g}"))
            
            # 收集該世代的所有設置
            generation_settings = []
            for alpha in alphalist.alpha_list:
                if alpha.name in self.alpha_to_genome:
                    setting_gene = self.alpha_to_genome[alpha.name].get("setting_gene", {})
                    if setting_gene:  # 只有當有設置基因時才計算
                        generation_settings.append(setting_gene)
            
            if generation_settings:
                # 計算多樣性統計
                diversity_stats = get_setting_diversity_stats(generation_settings)
                
                for param_name, stats in diversity_stats.items():
                    diversity_data.append({
                        'generation': g,
                        'parameter': param_name,
                        'unique_count': stats['unique_count'],
                        'total_count': stats['total_count'],
                        'diversity_ratio': stats['unique_count'] / stats['total_count'] if stats['total_count'] > 0 else 0
                    })
        
        if not diversity_data:
            print("沒有找到設置參數資料")
            return pd.DataFrame()
        
        df = pd.DataFrame(diversity_data)
        
        # 儲存結果
        df.to_csv(os.path.join(self.analysis_folder, "setting_diversity.csv"), index=False)
        
        # 繪製設置多樣性圖
        self._plot_setting_diversity(df)
        
        print(f"✓ 設置多樣性分析完成，結果已保存到 {self.analysis_folder}")
        return df
    
    def _plot_setting_diversity(self, df: pd.DataFrame) -> None:
        """繪製設置參數多樣性圖表"""
        if df.empty:
            return
        
        fig, axes = plt.subplots(2, 1, figsize=(12, 10))
        
        # Diversity ratio by generation
        pivot_df = df.pivot(index='generation', columns='parameter', values='diversity_ratio')
        
        for param in pivot_df.columns:
            axes[0].plot(pivot_df.index, pivot_df[param], marker='o', label=param)
        
        axes[0].set_title('Parameter Diversity Ratio by Generation')
        axes[0].set_xlabel('Generation')
        axes[0].set_ylabel('Diversity Ratio (unique/total)')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)
        
        # Unique value count by generation
        pivot_df_count = df.pivot(index='generation', columns='parameter', values='unique_count')
        
        for param in pivot_df_count.columns:
            axes[1].plot(pivot_df_count.index, pivot_df_count[param], marker='s', label=param)
        
        axes[1].set_title('Unique Value Count by Generation')
        axes[1].set_xlabel('Generation')
        axes[1].set_ylabel('Unique Value Count')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.analysis_folder, "setting_diversity.png"), dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ 基因多樣性圖已保存到: {self.analysis_folder}/genetic_diversity.png")
    
    def create_experiment_alphalist(self, corr_threshold: float = 0.7) -> AlphaList:
        """
        創建實驗代表性 AlphaList
        
        Args:
            corr_threshold: 相關係數閾值
            
        Returns:
            過濾後的 AlphaList
        """
        print("正在創建實驗代表性 AlphaList...")
        
        # 收集所有世代的 alpha
        all_alphas = AlphaList()
        generation_count = self._get_generation_count()
        
        for g in range(1, generation_count + 1):
            alphalist = AlphaList.load(os.path.join(self.research_folder, f"alphalist_{g}"))
            for alpha in alphalist.alpha_list:
                all_alphas.append(alpha)
        
        print(f"✓ 收集到 {len(all_alphas.alpha_list)} 個 alpha")
        
        # 使用 bcorr_filter 過濾
        filtered_alphalist = bcorr_filter(all_alphas, FitnessScore, corr_threshold=corr_threshold)
        
        # 保存過濾後的 alphalist
        filtered_alphalist.save(os.path.join(self.analysis_folder, "experiment_representative_alphalist"))
        
        print(f"✓ 過濾後保留 {len(filtered_alphalist.alpha_list)} 個代表性 alpha")
        print(f"✓ 實驗代表性 AlphaList 已保存到: {self.analysis_folder}/experiment_representative_alphalist")
        
        return filtered_alphalist
    
    def analyze_experiment_summary(self) -> Dict[str, Any]:
        """
        生成實驗總結報告
        
        Returns:
            包含實驗總結信息的字典
        """
        print("正在生成實驗總結報告...")
        
        generation_count = self._get_generation_count()
        
        # 載入世代分數演化數據
        score_evolution_path = os.path.join(self.analysis_folder, "generation_score_evolution.csv")
        if os.path.exists(score_evolution_path):
            score_df = pd.read_csv(score_evolution_path)
        else:
            score_df = self.analyze_generation_score_evolution()
        
        # 載入基因多樣性數據
        diversity_path = os.path.join(self.analysis_folder, "genetic_diversity.csv")
        if os.path.exists(diversity_path):
            diversity_df = pd.read_csv(diversity_path)
        else:
            diversity_df = self.analyze_genetic_diversity()
        
        # 創建實驗代表性 alphalist
        representative_alphalist = self.create_experiment_alphalist()
        
        # 生成總結報告
        summary = {
            'experiment_name': self.research_name,
            'total_generations': generation_count,
            'ga_config': self.ga_config,
            'score_evolution_summary': {
                'initial_fitness_mean': float(score_df.iloc[0]['fitness_mean']) if len(score_df) > 0 else None,
                'final_fitness_mean': float(score_df.iloc[-1]['fitness_mean']) if len(score_df) > 0 else None,
                'max_fitness_achieved': float(score_df['fitness_max'].max()) if len(score_df) > 0 else None,
                'fitness_improvement': float(score_df.iloc[-1]['fitness_mean'] - score_df.iloc[0]['fitness_mean']) if len(score_df) > 0 else None,
            },
            'diversity_summary': {
                'initial_entropy': float(diversity_df.iloc[0]['average_entropy']) if len(diversity_df) > 0 else None,
                'final_entropy': float(diversity_df.iloc[-1]['average_entropy']) if len(diversity_df) > 0 else None,
                'entropy_trend': 'increasing' if len(diversity_df) > 1 and diversity_df.iloc[-1]['average_entropy'] > diversity_df.iloc[0]['average_entropy'] else 'decreasing',
            },
            'representative_alphas': {
                'total_count': len(representative_alphalist.alpha_list),
                'top_fitness_scores': [float(FitnessScore.score(alpha)) for alpha in 
                                     sorted(representative_alphalist.alpha_list, 
                                           key=lambda x: FitnessScore.score(x), reverse=True)[:5]]
            }
        }
        
        # 保存總結報告
        with open(os.path.join(self.analysis_folder, "experiment_summary.json"), 'w') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✓ 實驗總結報告已保存到: {self.analysis_folder}/experiment_summary.json")
        
        return summary
    
    def run_full_analysis(self, corr_threshold: float = 0.7) -> Dict[str, Any]:
        """
        執行完整的實驗分析
        
        Args:
            corr_threshold: bcorr_filter 的相關係數閾值
            
        Returns:
            實驗總結報告
        """
        print(f"開始對實驗 '{self.research_name}' 進行完整分析...")
        print("=" * 60)
        
        # 1. 世代分數演化分析
        score_df = self.analyze_generation_score_evolution()
        
        # 2. 基因多樣性分析  
        diversity_df = self.analyze_genetic_diversity()
        
        # 3. 設置參數多樣性分析（如果有的話）
        setting_diversity_df = self.analyze_setting_diversity()
        
        # 4. 創建代表性 AlphaList
        alphalist = self.create_experiment_alphalist(corr_threshold)
        
        # 5. 生成實驗總結報告
        summary = self.analyze_experiment_summary()
        
        print("=" * 60)
        print("✓ 完整分析已完成！")
        print(f"✓ 所有結果已保存到: {self.analysis_folder}")
        
        return summary


def analyze_experiment_results(research_name: str, corr_threshold: float = 0.7) -> Dict[str, Any]:
    """
    分析實驗結果的主要函數
    
    Args:
        research_name: 研究名稱
        corr_threshold: bcorr_filter 的相關係數閾值
        
    Returns:
        實驗總結報告
    """
    analyzer = ExperimentAnalysis(research_name)
    return analyzer.run_full_analysis(corr_threshold)


def create_representative_alphalist(research_name: str, corr_threshold: float = 0.7) -> AlphaList:
    """
    創建代表性 AlphaList 的獨立函數
    
    Args:
        research_name: 研究名稱  
        corr_threshold: bcorr_filter 的相關係數閾值
        
    Returns:
        過濾後的 AlphaList
    """
    analyzer = ExperimentAnalysis(research_name)
    return analyzer.create_experiment_alphalist(corr_threshold)


def generate_experiment_summary_report(research_name: str) -> Dict[str, Any]:
    """
    生成實驗總結報告的獨立函數
    
    Args:
        research_name: 研究名稱
        
    Returns:
        實驗總結報告
    """
    analyzer = ExperimentAnalysis(research_name)
    return analyzer.analyze_experiment_summary() 