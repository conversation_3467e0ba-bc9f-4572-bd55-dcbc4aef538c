import abc
import numpy as np
from functools import partial

from alpha import Alpha


def piecewise_score(
    x,
    low_threshold: float,
    high_threshold: float,
    low_score: float,
    mid_slope: float,
    low_slope: float,
    high_slope: float,
    abs_value: bool = False
) -> np.ndarray:
    """
    通用三段線性函式：
      x <= low_threshold    : y = low_score + (x - low_threshold) * low_slope
      low_threshold < x < high_threshold : y = low_score + (x - low_threshold) * mid_slope
      x >= high_threshold   : y = high_score + (x - high_threshold) * high_slope
    high_score = low_score + (high_threshold - low_threshold) * mid_slope
    支援純量或 NumPy 陣列輸入，並可選擇取絕對值。
    """
    x = np.abs(x) if abs_value else x
    high_score = low_score + (high_threshold - low_threshold) * mid_slope

    # 先對三段分別計算
    y_low   = low_score  + (x - low_threshold)  * low_slope
    y_mid   = low_score  + (x - low_threshold)  * mid_slope
    y_high  = high_score + (x - high_threshold) * high_slope

    # 用 numpy.where 做條件選擇（向量化）
    return np.where(
        x <= low_threshold,
        y_low,
        np.where(x >= high_threshold, y_high, y_mid)
    )


cal_sharpe_score = partial(
    piecewise_score,
    low_threshold=1.5,
    high_threshold=5.0,
    low_score=0.0,
    mid_slope=1.0,
    low_slope=5.0,
    high_slope=0.05,
    abs_value=True
)

cal_turnover_score = partial(
    piecewise_score,
    low_threshold=0.3,
    high_threshold=0.7,
    low_score=0.0,
    mid_slope=-6.0,
    low_slope=-1.0,
    high_slope=-50.0,
    abs_value=False
)


class AlphaScore:
    def __init__(self):
        pass

    @classmethod
    @abc.abstractmethod
    def score(cls, alpha: Alpha) -> float:
        return NotImplemented

    @classmethod
    @abc.abstractmethod
    def name(self) -> str:
        return NotImplemented


class SharpeScore(AlphaScore):

    @classmethod
    def score(cls, alpha: Alpha) -> float:
        return alpha["sharpe"]

    @classmethod
    def name(cls) -> str:
        return "sharpe"
class AbsSharpeScore(AlphaScore):

    @classmethod
    def score(cls, alpha: Alpha) -> float:
        return abs(alpha["sharpe"])

    @classmethod
    def name(cls) -> str:
        return "abs_sharpe"

class FitnessScore(AlphaScore):

    @classmethod
    def score(cls, alpha: Alpha) -> float:
        return alpha["fitness"]

    @classmethod
    def name(cls) -> str:
        return "fitness"


class TurnoverScore(AlphaScore):

    @classmethod
    def score(cls, alpha: Alpha) -> float:
        return alpha["turnover"]

    @classmethod
    def name(cls) -> str:
        return "turnover"


class PiecewiseSharpeScore(AlphaScore):

    @classmethod
    def score(cls, alpha: Alpha) -> float:
        sharpe_value = alpha["sharpe"]
        # 對極值給予大懲罰
        if abs(sharpe_value) > 6:
            return -1000.0
        return float(cal_sharpe_score(sharpe_value))

    @classmethod
    def name(cls) -> str:
        return "piecewise_sharpe"


class PiecewiseTurnoverScore(AlphaScore):

    @classmethod
    def score(cls, alpha: Alpha) -> float:
        turnover_value = alpha["turnover"]
        # 對極值給予大懲罰
        if turnover_value > 0.9:
            return -1000.0
        return float(cal_turnover_score(turnover_value))

    @classmethod
    def name(cls) -> str:
        return "piecewise_turnover"


class CombinedPiecewiseScore(AlphaScore):

    @classmethod
    def score(cls, alpha: Alpha) -> float:
        sharpe_score = PiecewiseSharpeScore.score(alpha)
        turnover_score = PiecewiseTurnoverScore.score(alpha)
        return sharpe_score + turnover_score

    @classmethod
    def name(cls) -> str:
        return "combined_piecewise"
