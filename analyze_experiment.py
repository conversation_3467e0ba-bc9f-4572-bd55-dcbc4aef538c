import argparse
import os

from experiment_analysis import ExperimentAnalysis

def main():
    parser = argparse.ArgumentParser(description="分析實驗結果")
    parser.add_argument("--research_name", required=True, help="研究名稱")
    parser.add_argument("--corr_threshold", type=float, default=0.7, 
                       help="bcorr_filter 的相關係數閾值 (預設: 0.7)")
    parser.add_argument("--analysis_type", choices=['full', 'score', 'diversity', 'setting_diversity', 'alphalist', 'summary'],
                       default='full', help="分析類型")
    
    args = parser.parse_args()
    
    print(f"開始分析實驗: {args.research_name}")
    print("=" * 60)
    
    # 檢查研究資料夾是否存在
    from src.paths import ResearchPath
    research_folder = os.path.join(ResearchPath.RESEARCH_PROCESS, args.research_name)
    if not os.path.exists(research_folder):
        print(f"❌ 錯誤：找不到研究資料夾 {research_folder}")
        print("請確認研究名稱是否正確。")
        return
    
    try:
        analyzer = ExperimentAnalysis(args.research_name)
        
        if args.analysis_type == 'full':
            # 執行完整分析
            summary = analyzer.run_full_analysis(args.corr_threshold)
            print_summary(summary)
            
        elif args.analysis_type == 'score':
            # 只進行世代分數演化分析
            print("正在進行世代分數演化分析...")
            score_df = analyzer.analyze_generation_score_evolution()
            print(f"✓ 分析完成，結果已保存到 {analyzer.analysis_folder}")
            
        elif args.analysis_type == 'diversity':
            # 只進行基因多樣性分析
            print("正在進行基因多樣性分析...")
            diversity_df = analyzer.analyze_genetic_diversity()
            print(f"✓ 分析完成，結果已保存到 {analyzer.analysis_folder}")
            
        elif args.analysis_type == 'setting_diversity':
            # 只進行設置參數多樣性分析
            print("正在進行設置參數多樣性分析...")
            setting_diversity_df = analyzer.analyze_setting_diversity()
            print(f"✓ 分析完成，結果已保存到 {analyzer.analysis_folder}")
            
        elif args.analysis_type == 'alphalist':
            # 只創建代表性 AlphaList
            print("正在創建代表性 AlphaList...")
            alphalist = analyzer.create_experiment_alphalist(args.corr_threshold)
            print(f"✓ 創建完成，共 {len(alphalist.alpha_list)} 個代表性 alpha")
            
        elif args.analysis_type == 'summary':
            # 只生成總結報告
            print("正在生成實驗總結報告...")
            summary = analyzer.analyze_experiment_summary()
            print_summary(summary)
            
    except Exception as e:
        print(f"❌ 分析過程中發生錯誤：{e}")
        import traceback
        traceback.print_exc()


def print_summary(summary):
    """列印實驗總結"""
    print("\n實驗分析完成！總結如下：")
    print("-" * 50)
    print(f"實驗名稱: {summary['experiment_name']}")
    print(f"總世代數: {summary['total_generations']}")
    
    if summary['score_evolution_summary']['initial_fitness_mean'] is not None:
        print(f"初始平均 Fitness: {summary['score_evolution_summary']['initial_fitness_mean']:.6f}")
        print(f"最終平均 Fitness: {summary['score_evolution_summary']['final_fitness_mean']:.6f}")
        print(f"Fitness 改善幅度: {summary['score_evolution_summary']['fitness_improvement']:.6f}")
        print(f"最高 Fitness: {summary['score_evolution_summary']['max_fitness_achieved']:.6f}")
    
    print(f"代表性 Alpha 數量: {summary['representative_alphas']['total_count']}")
    if summary['representative_alphas']['top_fitness_scores']:
        top_scores = [f'{score:.6f}' for score in summary['representative_alphas']['top_fitness_scores']]
        print(f"前5名 Fitness 分數: {top_scores}")
    
    research_name = summary['experiment_name']
    print(f"\n✓ 所有分析結果已保存到: DB/ResearchDB/research_process/{research_name}/analysis/")
    print("  - generation_score_evolution.png: 世代分數演化圖")
    print("  - genetic_diversity.png: 基因多樣性分析圖")
    print("  - setting_diversity.png: 設置參數多樣性分析圖（如果適用）")
    print("  - generation_score_evolution.csv: 世代分數演化數據")
    print("  - genetic_diversity.csv: 基因多樣性數據")
    print("  - setting_diversity.csv: 設置參數多樣性數據（如果適用）")
    print("  - experiment_summary.json: 實驗總結報告")
    print("  - experiment_representative_alphalist: 代表性AlphaList")


if __name__ == "__main__":
    main() 