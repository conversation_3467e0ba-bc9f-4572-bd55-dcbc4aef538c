# Setting Up Your First Alpha Research Machine
This document is for setting up your machine to run the project. Here are the steps to set up your machine:

## 0. Using Cloud Infrastructure

This project recommends using AWS EC2 to run the project. You can refer to the [CLOUD_INFRA.md](doc/CLOUD_INFRA.md) file for more details.

## 1. Set up the python environment

This project uses `uv` to manage the Python virtual environment and dependencies. The `pyproject.toml` file defines the project requirements, and `uv.lock` ensures everyone has the exact same environment.

1. **Install `uv`:**  
   If you haven't installed `uv`, please refer to the [official guide](https://github.com/astral-sh/uv).

2. **Create a virtual environment:**  
   This command reads the settings in `pyproject.toml` and creates a virtual environment with a compatible Python version.
   ```bash
   uv venv
   ```

3. **Activate the virtual environment:**  
   ```bash
   # macOS / Linux
   source .venv/bin/activate
   ```

4. **Install dependencies:**  
   Use `uv sync` with the `uv.lock` file to precisely reproduce the development environment and ensure all developers have exactly the same package versions.
   ```bash
   uv sync
   ```

<details>
<summary><span style="font-size:1.3em; font-weight:bold;">More details about uv usage</span></summary>

### Daily development and dependency management

**It is strongly recommended to use `uv add` and `uv remove` to add or remove project dependencies.**

- **Add a package:**  
This command will automatically add `pandas` to `pyproject.toml`, install it, and update the `uv.lock` file.
    ```bash
    uv add pandas
    ```

- **Remove a package:**  
This command will remove the package definition from `pyproject.toml` and update the `uv.lock` file.
    ```bash
    uv remove pandas
    ```

### How to run Python scripts with uv

You can activate the virtual environment with `source .venv/bin/activate` and use `python` to run scripts, or directly use `uv run`:

- **Run the main program:**
    ```bash
    source .venv/bin/activate
    python python_code.py
    ```
    ```bash
    uv run python_code.py
    ```

> `uv run` will automatically execute the command in the project's virtual environment, whether or not you have activated `.venv`.

</details>

## 2. Set up your `.env` file with your WQ account and password
This project uses the `.env` file to store the environment variables for security reason and to avoid hardcoding the credentials in the code. You can use the `dotenv` library to load the environment variables.

1. Create a `.env` file in the root directory. The file should contain the following fields :

    ```
    WQ_ACCOUNT=""
    WQ_PASSWORD=""

    TELEGRAM_BOT_API_TOKEN=""
    ```

2. Fill `WQ_ACCOUNT` and `WQ_PASSWORD` in the `.env` file as below
    ```bash
    WQ_ACCOUNT="<EMAIL>"
    WQ_PASSWORD="your_wq_password"
    ```

## 3. Set up your telegram bot
This project uses the telegram bot as frontend to interact with the user. If you don't have a telegram account yet, you can create one with your phone number.

1. Create a telegram bot by talking to [@BotFather](https://t.me/BotFather) and get the `TELEGRAM_BOT_API_TOKEN`. You can follow the [tutorial](https://hackmd.io/@truckski/HkgaMUc24?type=view) to get a token (you only need to do the first part)

2. Click your bot url and click `Start` to start the bot.

3. Fill `TELEGRAM_BOT_API_TOKEN` in the `.env` file as below
    ```bash
    TELEGRAM_BOT_API_TOKEN="your_telegram_bot_api_token"
    ```

3. Get the `TELEGRAM_CHAT_ID` by running the `set_telegram_chat_id.py` script, this script will automatically write the `TELEGRAM_CHAT_ID` to the `.env` file
    ```bash
    python set_telegram_chat_id.py
    ```

4. Check the `.env` file to make sure all the variables are set correctly as below
    ```
    WQ_ACCOUNT="<EMAIL>"
    WQ_PASSWORD="your_wq_password"

    TELEGRAM_BOT_API_TOKEN="your_telegram_bot_api_token"
    TELEGRAM_CHAT_ID="your_telegram_chat_id"
    ```

## 4. Start your journey on alpha research

You can use `tmux` to run your program in the background, so it keeps running even if you close your terminal or VSCode.  
For more details, you can refer to this tutorial: [tmux basic usage](https://hackmd.io/@Cheng-Hao/Hyk9f6mZd).

1. **Run the main program for simulation bot:**
   ```bash
   python main_code.py
   ```

2. **Run the sample script for genetic algorithm research:**
   ```bash
   python sample_script.py --research_name real_test --template_file default_template.json --setting_file default_setting.json --ga_config_file default_ga_config.json
   ```
   You can customize parameters via command-line arguments. For example:
   ```bash
   python sample_script.py --research_name my_experiment --template_file custom_template.json
   ```

### Variable introduction

#### Geneticalgorithm.run()
- **auto_run:** If `True`, it will not output a sample for you to check your expression
- **exist_ok:** If `True`, it will not overwrite the previous research process

#### Worker.run_all()
- **max_thread:** How many alphas are simulated at the same time
- **reset_or_not:** If `True`, it will delete the alpha in running
- **get_pnl_or_not:** If `True`, it will get the PnL

### Running the Data Sync Script

To synchronize all simulation settings, datasets, and data fields from WorldQuant Brain, and store them in a dated folder with a copy in 'latest':

```bash
python run_data_sync.py
```

This script will:
- Download data to a folder named with today's date (YYYY-MM-DD).
- Copy the folder to 'latest' for easy access to the most recent data.

Note: The script uses a simplified mode by default (sync_all(simplified=True)) to download only the top simulation option per region.

## 5. Managing Configuration Files in ResearchDB

The project uses JSON files in `DB/ResearchDB/` for modular configurations:
- `template/`: Contains template JSON files (e.g., `default_template.json`) with template strings and gene pools.
- `setting/`: Contains setting JSON files (e.g., `default_setting.json`) for payload settings.
- `ga_config/`: Contains GA config JSON files (e.g., `default_ga_config.json`) for genetic algorithm parameters.

These default files are included in the repository. To customize:
1. Create your own JSON files in the respective directories (e.g., copy `default_template.json` to `my_template.json` and edit it).
2. Run `sample_script.py` with the appropriate arguments, e.g., `--template_file my_template.json`.

Note: The `research_process/` and `data/` directories are ignored by Git and generated during runtime. Do not add sensitive data there.


