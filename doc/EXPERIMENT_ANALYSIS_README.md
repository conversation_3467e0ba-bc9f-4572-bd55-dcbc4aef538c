# 實驗結果分析功能說明

本項目新增了完整的實驗結果分析功能，可以對遺傳演算法實驗進行深入分析，包括世代分數演化、基因多樣性分析，以及創建代表性的 Alpha 集合。

## 功能特色

### 🔍 世代分數演化分析
- 分析每一代的 Fitness、Sharpe Ratio、Turnover 等指標變化
- 生成詳細的演化趨勢圖表，包含平均值、標準差、最大值、分位數等統計信息
- 視覺化展示演算法的收斂情況和性能提升趨勢

### 🧬 基因多樣性分析
- 計算每一代基因的多樣性指標（Shannon Entropy、唯一值比例）
- 分析基因位點的演化趨勢，了解演算法的探索與利用平衡
- 生成基因多樣性熱圖，視覺化不同基因位點的變化

### 📊 Alpha 集合分析
- 收集所有世代的 Alpha 並使用 bcorr_filter 過濾高相關性的 Alpha
- 使用 FitnessScore 作為評分標準，相關係數閾值設為 0.7
- 生成代表性的 Alpha 集合，便於後續分析和使用

### 📈 視覺化圖表
- 所有圖表使用英文標籤，避免中文顯示問題
- 高解析度 PNG 格式輸出（300 DPI）
- 專業的科學圖表樣式設計

## 安裝依賴

確保安裝了所需的依賴套件：

```bash
# 如果使用 uv
uv sync

# 或者使用 pip
pip install matplotlib>=3.7.0 numpy>=1.24.0 seaborn>=0.12.0
```

## 使用方法

### 方法 1：自動分析（推薦）

修改後的 `sample_script.py` 會在實驗完成後自動進行分析：

```bash
python sample_script.py --research_name my_experiment --template_file my_template.json
```

實驗完成後會自動執行分析並顯示結果摘要。

### 方法 2：獨立分析

使用專用的分析腳本對已完成的實驗進行分析：

```bash
# 完整分析
python analyze_experiment.py --research_name my_experiment

# 指定相關係數閾值
python analyze_experiment.py --research_name my_experiment --corr_threshold 0.8

# 只進行特定類型的分析
python analyze_experiment.py --research_name my_experiment --analysis_type score
python analyze_experiment.py --research_name my_experiment --analysis_type diversity
python analyze_experiment.py --research_name my_experiment --analysis_type alphalist
python analyze_experiment.py --research_name my_experiment --analysis_type summary
```

### 方法 3：程式化調用

在 Python 程式中直接使用分析功能：

```python
from src.experiment_analysis import (
    analyze_experiment_results,
    create_representative_alphalist,
    ExperimentAnalysis
)

# 完整分析
summary = analyze_experiment_results("my_experiment", corr_threshold=0.7)

# 只創建代表性 AlphaList
alphalist = create_representative_alphalist("my_experiment", corr_threshold=0.7)

# 自定義分析
analyzer = ExperimentAnalysis("my_experiment")
score_df = analyzer.analyze_generation_score_evolution()
diversity_df = analyzer.analyze_genetic_diversity()
```

## 輸出文件說明

分析結果會保存在 `DB/ResearchDB/research_process/{research_name}/analysis/` 目錄下：

### 📊 圖表文件
- `generation_score_evolution.png`：世代分數演化圖
  - 包含 Fitness 分數演化、Sharpe Ratio 演化、Turnover 演化、Alpha 數量變化四個子圖
- `genetic_diversity.png`：基因多樣性分析圖
  - 包含平均熵演化、唯一值比例演化、基因數量變化、多樣性熱圖四個子圖

### 📄 數據文件
- `generation_score_evolution.csv`：世代分數演化的詳細數據
- `genetic_diversity.csv`：基因多樣性的詳細數據
- `experiment_summary.json`：實驗總結報告（JSON 格式）

### 🧬 Alpha 集合
- `experiment_representative_alphalist`：過濾後的代表性 AlphaList（pickle 格式）

## 分析指標說明

### 世代分數演化指標
- **fitness_mean/std/max/min**：每代 Fitness 分數的統計信息
- **fitness_q75/q25**：Fitness 分數的四分位數
- **sharpe_mean/std/max**：Sharpe Ratio 的統計信息
- **turnover_mean**：平均 Turnover
- **alpha_count**：每代有效 Alpha 數量

### 基因多樣性指標
- **average_entropy**：平均 Shannon 熵，衡量基因的整體多樣性
- **average_unique_ratio**：平均唯一值比例，衡量基因位點的變異程度
- **{gene_key}_entropy**：特定基因位點的熵值
- **{gene_key}_unique_ratio**：特定基因位點的唯一值比例
- **gene_count**：每代基因數量

## 實驗評估指南

### 🎯 好的實驗特徵
1. **Fitness 分數持續改善**：隨著世代增加，平均和最大 Fitness 分數呈上升趨勢
2. **收斂穩定**：後期世代的分數變化趨於平緩，標準差減小
3. **多樣性平衡**：初期保持較高的基因多樣性，後期適度收斂
4. **高品質 Alpha**：代表性 Alpha 集合中有高分數的 Alpha

### ⚠️ 需要注意的情況
1. **過早收斂**：前幾代就失去基因多樣性，可能陷入局部最優
2. **無收斂趨勢**：Fitness 分數沒有明顯改善趨勢
3. **震盪不穩**：分數大幅波動，缺乏穩定的改善方向
4. **多樣性過低**：基因熵值過早降至很低水平

## 自定義分析

可以繼承 `ExperimentAnalysis` 類別來實現自定義分析：

```python
from src.experiment_analysis import ExperimentAnalysis

class CustomAnalysis(ExperimentAnalysis):
    def custom_analysis(self):
        # 自定義分析邏輯
        pass
```

---

如有問題或建議，請在項目中提出 Issue。 