# 如何部署機器在Cloud Infrastructure上

## 1. 為何需要部署機器在Cloud Infrastructure上

在雲端上部署你的機器有以下幾個主要原因：

- **網路連線穩定**：雲端服務提供商通常擁有高品質的網路基礎設施，確保你的機器可以長時間穩定地連接到網際網路，減少因網路不穩定導致的服務中斷。

- **不會斷電**：雲端資料中心具備完善的電力備援系統，極少發生斷電情況，讓你的服務可以持續運作，提升可靠性。

## 2. 選擇雲端服務供應商

目前主流的雲端服務供應商包括：

- **AWS（Amazon Web Services）**
- **Microsoft Azure**
- **Google Cloud Platform（GCP）**

本教學將以 **AWS** 為例進行說明（AWS沒有贊助，就是單純覺得AWS的免費方案適合我們的需求）。

你可以在 [AWS 免費方案官方頁面](https://aws.amazon.com/tw/free/?all-free-tier.sort-by=item.additionalFields.SortRank&all-free-tier.sort-order=asc&awsf.Free%20Tier%20Types=*all&awsf.Free%20Tier%20Categories=*all) 查詢所有可用的免費資源。

> **注意**：以我們目前的需求來說，機器不需要高階資源，因此 AWS 的免費方案已足夠，像是本專案會用到的 EC2 服務 AWS 每月提供 750 小時的 t2.micro 或 t3.micro，以及30GB的EBS。

若有特殊需求，也可以考慮 AWS 的付費方案，或根據預算與地區選擇其他雲端服務商。如果你希望完全掌控並買斷硬體資源，也可以選擇自行架設伺服器，但這會增加維運與管理的複雜度。

**建議**：根據你的預算、技術需求、地理位置與未來擴展性，評估最適合自己的雲端平台。

## 3. 建立你的第一個EC2

### 3.1 註冊與登入AWS

1. 進入 [AWS官網](https://aws.amazon.com/tw/)
2. 點擊右上角的「登入主控台」
3. 選擇「建立帳號」
4. 填寫相關資料，完成註冊

### 3.2 切換地區到「台北」

> **注意**：我不太確定這個步驟是否必要，要看WQ平台規定是不是台灣顧問只能從台灣的網域連線

1. 點選頁面右上角的地區選擇
2. 選擇「亞太地區 (台北)」
3. 如果沒有請先至「管理區域」啟用「台北」

> **提醒**：這個過程可能需要等待幾分鐘，請耐心等待（我等了5分鐘左右）

### 3.3 建立你的第一個「EC2」

#### 步驟一：進入EC2服務

1. 點擊頁面左上角的「搜尋」
2. 輸入「EC2」
3. 點擊「EC2」進入服務頁面
4. 在左側選單中選擇「執行個體」>「執行個體」
5. 點擊「啟動執行個體」按鈕

#### 步驟二：設定執行個體

依序設定以下選項（建議選擇有「符合免費方案資格」標示的選項）：

| 設定項目 | 建議值 |
|---------|--------|
| **名稱和標籤** | 輸入「WQ-EC2」（名稱可自訂） |
| **AMI** | Ubuntu Server 24.04 LTS (HVM), EBS General Purpose (SSD) Volume Type 64-bit (x86) |
| **執行個體類型** | t3.micro |
| **金鑰對** | 建立新的金鑰對，名稱輸入「wq-key」，金鑰對類型選「RSA」，私有金鑰檔案格式選「.pem」 |
| **網路設定** | 保持預設即可 |
| **儲存空間** | AWS 免費提供 30GB，建議容量設為「30」，類型選「gp3」 |
| **進階詳細資訊** | 保持預設即可 |

> **重要提醒**：建立金鑰對後會自動下載「wq-key.pem」檔案。這個檔案是你連線 EC2 的鑰匙，請妥善保存，遺失後無法重新取得。

![EC2 建立步驟範例畫面](images/aws_ec2.png)

#### 步驟三：啟動執行個體 & 取得連線用IP

1. 確認所有設定無誤後，點擊「啟動執行個體」
2. 建立成功後，點擊「檢視所有執行個體」
3. 找到剛剛建立的執行個體，等待狀態檢查變成「執行中」
4. 在「執行個體ID」下面點選你的EC2
5. 點選「連線」>「SSH 用戶端」確認「4.使用 公有 DNS 連線至執行個體」，那串字可以當作是你的EC2的公開IP，我們需要這個IP來連線到你的EC2。



## 4. 使用IDE連線到你的EC2

這邊你可以用你順手的IDE，這邊以 **VSCode** 為例。

### 4.1 安裝VSCode與必要擴充功能

1. 下載 [VSCode](https://code.visualstudio.com/download) 並安裝
2. 安裝 [Remote - SSH](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-ssh) extension

### 4.2 設定SSH連線

1. 在VSCode中，點擊左下角的「><」圖示，或按下 `Ctrl + Shift + P`（Windows）或 `Command + Shift + P`（Mac）打開命令面板
2. 輸入 `Remote-SSH: Connect to Host...`
3. 選擇 `設定SSH 主機`，點開config檔案
4. 輸入以下內容：

```ssh
Host wq-ec2
    HostName 你的EC2的公有 DNS 
    User ubuntu
    IdentityFile ~/.ssh/wq-key.pem
```

> **注意**：
> - 記得把你的EC2的公開IP填入HostName
> - 把你的「wq-key.pem」檔案路徑填入IdentityFile
> - 另外記得把「wq-key.pem」的檔案權限改成400（只有使用者可以讀取），不然好像會連線失敗

#### 設定檔案權限

**mac/linux:**
```bash
chmod 400 ~/.ssh/wq-key.pem
```

**windows:** 我不太確定且沒有Windows電腦可以測試，請自行google或ChatGPT

### 4.3 連線到EC2

1. 儲存config檔案
2. 再次按下 `Ctrl + Shift + P`（Windows）或 `Command + Shift + P`（Mac）打開命令面板
3. 輸入 `Remote-SSH: Connect to Host...`
4. 點擊剛剛建立的 `wq-ec2`
5. 選擇「繼續」

連線成功後，你就可以在VSCode中使用你剛剛建立的EC2了。

## 5. 啟用WQ Open Machine的前置作業

### 5.1 安裝python環境管理工具uv

連線到你的EC2後，先安裝 `uv`：

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

重開終端機後，就可以使用 `uv` 來安裝python環境了。

### 5.2 上傳WQ Open Machine的程式碼並解壓縮

#### 安裝unzip（如果沒有安裝）

```bash
sudo apt install unzip
```

#### 上傳並解壓縮程式碼

1. 上傳 `WQ_open_machine_TW.zip` 到你的EC2
   - 可以在VSCode中使用拖拉的方式上傳
   - 也可以使用 `scp` 命令上傳
2. 使用 `unzip` 解壓縮：

```bash
unzip WQ_open_machine_TW.zip
```

### 5.3 後續步驟

接下來請照著WQ Open Machine的README的步驟繼續進行：

- [中文版](README_zh.md)
- [英文版](../README.md)



