# AlphaListParser 使用說明

`AlphaListParser` 是一個靈活的工具，用於將 `AlphaList` 中的 `Alpha` 物件轉換為 `pandas.DataFrame`，支援透過自訂函數動態設定要提取的欄位。

## 核心特色

- **完全可自訂**: 透過函數定義任何您想要的欄位
- **動態管理**: 可隨時添加、移除或修改欄位提取器
- **錯誤處理**: 自動處理提取過程中的錯誤
- **靈活提取**: 支援簡單欄位、複合計算、分類標籤等

## 基本使用

```python
from alphalist_parser import AlphaListParser
from alphalist import AlphaList

# 載入 AlphaList
alphalist = AlphaList.load("path/to/your/alphalist")

# 使用預設欄位提取器
parser = AlphaListParser(alphalist)
df = parser.to_dataframe()
print(df.head())
```

## 自訂欄位提取器

```python
# 定義您自己的欄位提取器
custom_extractors = {
    # 基本欄位
    'name': lambda alpha: alpha.get_name(),
    'sharpe': lambda alpha: alpha.get_sharpe(),
    'fitness': lambda alpha: alpha.get_fitness(),
    
    # 複合計算欄位
    'risk_adjusted_return': lambda alpha: alpha.get_sharpe() * alpha.get_fitness(),
    
    # 分類欄位
    'performance_level': lambda alpha: (
        'high' if alpha.get_sharpe() > 1.5 else 
        'medium' if alpha.get_sharpe() > 0.5 else 
        'low'
    ),
    
    # 檢查欄位
    'has_result': lambda alpha: alpha.result is not None,
    
    # 從 payload 提取資料
    'strategy_type': lambda alpha: alpha.payload.get('settings', {}).get('strategy', 'unknown'),
}

parser = AlphaListParser(alphalist, custom_extractors)
df = parser.to_dataframe()
```

## 動態欄位管理

```python
# 創建基本解析器
parser = AlphaListParser(alphalist)

# 動態添加欄位
parser.add_field('alpha_id', lambda alpha: alpha.get_id())
parser.add_field('turnover_category', lambda alpha: 'high' if alpha.get_turnover() > 0.5 else 'low')

# 移除欄位
parser.remove_field('fitness')

# 查看所有欄位
print("目前欄位:", parser.get_field_names())

# 轉換為 DataFrame
df = parser.to_dataframe()
```

## 錯誤處理和安全模式

```python
# 安全提取器 - 處理可能的錯誤
safe_extractors = {
    'safe_sharpe': lambda alpha: (
        alpha.get_sharpe() if alpha.result is not None 
        else None
    ),
    'safe_fitness': lambda alpha: (
        alpha.get_fitness() if hasattr(alpha, 'get_fitness') and alpha.result is not None 
        else None
    ),
}

parser = AlphaListParser(alphalist, safe_extractors)

# 使用安全模式，自動移除無效的 Alpha
df_safe = parser.to_dataframe_safe(drop_invalid=True)
```

## 可用的範例提取器

```python
# 查看內建的範例提取器程式碼
examples = parser.get_extractor_examples()
for field, code in examples.items():
    print(f"{field}: {code}")
```

## API 參考

### 主要方法

- `__init__(alphalist, field_extractors=None)`: 初始化解析器
- `to_dataframe()`: 轉換為 DataFrame
- `to_dataframe_safe(drop_invalid=True)`: 安全轉換，可選擇移除無效資料
- `add_field(field_name, extractor)`: 添加欄位提取器
- `remove_field(field_name)`: 移除欄位提取器
- `get_field_names()`: 取得所有欄位名稱
- `has_field(field_name)`: 檢查是否存在指定欄位
- `get_extractor_examples()`: 取得範例提取器程式碼

### 欄位提取器函數格式

欄位提取器應該是一個函數，接受一個 `Alpha` 物件作為參數，並返回該欄位的值：

```python
def my_extractor(alpha: Alpha) -> Any:
    # 您的提取邏輯
    return some_value

# 或使用 lambda
my_extractor = lambda alpha: alpha.get_some_field()
```

## 範例場景

### 1. 策略比較分析
```python
extractors = {
    'name': lambda alpha: alpha.get_name(),
    'sharpe': lambda alpha: alpha.get_sharpe(),
    'max_drawdown': lambda alpha: alpha['drawdown'],
    'strategy_type': lambda alpha: alpha.payload.get('settings', {}).get('strategy'),
}
```

### 2. 風險調整後績效
```python
extractors = {
    'name': lambda alpha: alpha.get_name(),
    'risk_adjusted_score': lambda alpha: alpha.get_sharpe() / max(abs(alpha['drawdown']), 0.01),
    'efficiency': lambda alpha: alpha.get_fitness() / max(alpha.get_turnover(), 0.001),
}
```

### 3. 分類和標籤
```python
extractors = {
    'name': lambda alpha: alpha.get_name(),
    'performance_tier': lambda alpha: (
        'A' if alpha.get_sharpe() > 2 else
        'B' if alpha.get_sharpe() > 1 else
        'C'
    ),
    'risk_level': lambda alpha: (
        'high' if alpha.get_turnover() > 0.7 else
        'medium' if alpha.get_turnover() > 0.3 else
        'low'
    ),
}
```

這個設計讓您可以根據 `alpha.py` 中的任何可用屬性和方法，完全自訂您需要的欄位和提取邏輯！ 