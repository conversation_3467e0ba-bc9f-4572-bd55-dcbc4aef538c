# 設置您的第一台 Alpha 研究機器
本文檔說明如何設置您的機器來運行此專案。以下是設置機器的步驟：

## 0. 使用雲端基礎設施

本專案建議使用 AWS EC2 來運行專案。您可以參考 [CLOUD_INFRA.md](CLOUD_INFRA.md) 文件以獲取更多詳細資訊。

## 1. 設置 Python 環境

此專案使用 `uv` 來管理 Python 虛擬環境和依賴項。`pyproject.toml` 文件定義了專案需求，而 `uv.lock` 確保每個人都有完全相同的環境。

1. **安裝 `uv`：**  
   如果您尚未安裝 `uv`，請參考[官方指南](https://github.com/astral-sh/uv)。

2. **創建虛擬環境：**  
   此命令會讀取 `pyproject.toml` 中的設置，並創建一個兼容 Python 版本的虛擬環境。
   ```bash
   uv venv
   ```

3. **啟動虛擬環境：**  
   ```bash
   # macOS / Linux
   source .venv/bin/activate
   ```

4. **安裝依賴項：**  
   使用 `uv sync` 與 `uv.lock` 文件精確重現開發環境，確保所有開發者都有完全相同的套件版本。
   ```bash
   uv sync
   ```

<details>
<summary><span style="font-size:1.3em; font-weight:bold;">關於 uv 使用的更多詳細資訊</span></summary>

### 日常開發和依賴項管理

**強烈建議使用 `uv add` 和 `uv remove` 來新增或移除專案依賴項。**

- **新增套件：**  
此命令會自動將 `pandas` 新增到 `pyproject.toml`，安裝它並更新 `uv.lock` 文件。
    ```bash
    uv add pandas
    ```

- **移除套件：**  
此命令會從 `pyproject.toml` 中移除套件定義並更新 `uv.lock` 文件。
    ```bash
    uv remove pandas
    ```

### 如何使用 uv 運行 Python 腳本

您可以使用 `source .venv/bin/activate` 啟動虛擬環境並使用 `python` 運行腳本，或直接使用 `uv run`：

- **運行主程式：**
    ```bash
    source .venv/bin/activate
    python python_code.py
    ```
    ```bash
    uv run python_code.py
    ```

> `uv run` 會自動在專案的虛擬環境中執行命令，無論您是否已啟動 `.venv`。

</details>

## 2. 設置您的 `.env` 文件與 WQ 帳號和密碼
此專案使用 `.env` 文件來儲存環境變數，出於安全考量並避免在代碼中硬編碼憑證。您可以使用 `dotenv` 函式庫來載入環境變數。

1. 在根目錄中創建一個 `.env` 文件。該文件應包含以下欄位：

    ```
    WQ_ACCOUNT=""
    WQ_PASSWORD=""

    TELEGRAM_BOT_API_TOKEN=""
    ```

2. 在 `.env` 文件中填入 `WQ_ACCOUNT` 和 `WQ_PASSWORD`，如下所示：
    ```bash
    WQ_ACCOUNT="<EMAIL>"
    WQ_PASSWORD="your_wq_password"
    ```

## 3. 設置您的 Telegram 機器人
此專案使用 Telegram 機器人作為前端與用戶互動。如果您還沒有 Telegram 帳號，可以使用您的手機號碼創建一個。

1. 通過與 [@BotFather](https://t.me/BotFather) 對話創建 Telegram 機器人並獲取 `TELEGRAM_BOT_API_TOKEN`。您可以按照[教學](https://hackmd.io/@truckski/HkgaMUc24?type=view)來獲取令牌（您只需要執行第一部分）

2. 點擊您的機器人網址並點擊 `Start` 來啟動機器人。

3. 在 `.env` 文件中填入 `TELEGRAM_BOT_API_TOKEN`，如下所示：
    ```bash
    TELEGRAM_BOT_API_TOKEN="your_telegram_bot_api_token"
    ```

3. 通過運行 `set_telegram_chat_id.py` 腳本來獲取 `TELEGRAM_CHAT_ID`，此腳本會自動將 `TELEGRAM_CHAT_ID` 寫入 `.env` 文件
    ```bash
    python set_telegram_chat_id.py
    ```

4. 檢查 `.env` 文件以確保所有變數都正確設置，如下所示：
    ```
    WQ_ACCOUNT="<EMAIL>"
    WQ_PASSWORD="your_wq_password"

    TELEGRAM_BOT_API_TOKEN="your_telegram_bot_api_token"
    TELEGRAM_CHAT_ID="your_telegram_chat_id"
    ```

## 4. 開始您的 Alpha 研究之旅

您可以使用 `tmux` 將程式掛在背景執行，這樣即使關閉終端機或VSCode，程式也會持續運作。  
建議參考這篇教學：[tmux 基本用法](https://hackmd.io/@Cheng-Hao/Hyk9f6mZd)。

1. **運行模擬機器人的主程式：**
   ```bash
   python main_code.py
   ```

2. **運行遺傳演算法研究的範例腳本：**
   ```bash
   python sample_script.py --research_name real_test --template_file default_template.json --setting_file default_setting.json --ga_config_file default_ga_config.json
   ```
   您可以通過命令列參數自訂設定。例如：
   ```bash
   python sample_script.py --research_name my_experiment --template_file custom_template.json
   ```

### 變數介紹

#### Geneticalgorithm.run()
- **auto_run：** 如果為 `True`，將不會輸出樣本供您檢查表達式
- **exist_ok：** 如果為 `True`，將不會覆蓋之前的研究過程

#### Worker.run_all()
- **max_thread：** 同時模擬的 Alpha 數量
- **reset_or_not：** 如果為 `True`，將刪除運行中的 Alpha
- **get_pnl_or_not：** 如果為 `True`，將獲取損益 

### 執行資料同步腳本

若要同步 WorldQuant Brain 的所有模擬設定、資料集與資料欄位，並將結果儲存於以當天日期命名的資料夾，同時複製一份到 'latest'：

```bash
python run_data_sync.py
```

此腳本會：
- 將資料下載到以今天日期（YYYY-MM-DD）命名的資料夾。
- 再將該資料夾複製一份到 'latest'，方便快速取得最新資料。

注意：此腳本預設使用簡化模式（sync_all(simplified=True)），只會下載每個 region 最上層的模擬設定。 

## 5. 管理 ResearchDB 中的設定檔案

此專案使用 `DB/ResearchDB/` 中的 JSON 檔案來進行模組化設定：
- `template/`：包含模板 JSON 檔案（例如 `default_template.json`），內有模板字串和基因池。
- `setting/`：包含設定 JSON 檔案（例如 `default_setting.json`），用於 payload 設定。
- `ga_config/`：包含 GA 設定 JSON 檔案（例如 `default_ga_config.json`），用於遺傳演算法參數。

這些預設檔案已包含在儲存庫中。要自訂：
1. 在相應目錄中創建自己的 JSON 檔案（例如，複製 `default_template.json` 為 `my_template.json` 並編輯它）。
2. 使用適當的參數運行 `sample_script.py`，例如 `--template_file my_template.json`。

注意：`research_process/` 和 `data/` 目錄被 Git 忽略，並在運行時生成。請勿在此處新增敏感資料。 