# 設置參數優化功能更新

## 概要
已為遺傳算法新增設置參數優化功能，**完全向後兼容**現有程式碼。

## 主要功能
✅ **同時優化基因池和設置參數**  
✅ **自動偵測設置檔案格式**  
✅ **保持100%向後兼容性**  

## 使用方法

### 原有方式（固定設置）- 無需修改
```bash
python sample_script.py -s "GLB_MINVOL1M_1_STATISTICAL.json"
```

### 新方式（設置優化）- 只需更換設置檔案
```bash
python sample_script.py -s "GLB_MINVOL1M_1_OPT.json"
```

## 設置檔案格式對比

**固定設置檔案**（原有格式）：
```json
{
  "decay": 0,
  "neutralization": "STATISTICAL"
}
```

**優化設置檔案**（新格式）：
```json
{
  "decay": [0, 2, 4, 6, 8],
  "neutralization": ["STATISTICAL", "MARKET", "SECTOR"]
}
```

## 技術實作
- 整合功能到現有的 `generation_algorithm.py`
- 擴展 `experiment_analysis.py` 支援設置多樣性分析
- 更新 `sample_script.py` 和 `analyze_experiment.py`
- 新增 `setting_diversity` 分析類型

## 兼容性保證
- 所有現有程式碼無需修改即可正常運行
- 現有實驗資料和分析結果完全兼容
- 可漸進式採用新功能 