# 專案總覽

本專案為 WorldQuant Alpha 研究自動化平台，整合資料同步、遺傳演算法、模擬、分析與 Telegram 機器人通知等功能。以下為專案主要目錄與檔案說明：

## 目錄結構與用途

- `main_code.py`  
  啟動主模擬流程，執行所有待處理的 alpha 任務。

- `sample_script.py`  
  遺傳演算法研究的範例腳本，支援參數化設定與自動分析。

- `run_data_sync.py`  
  一鍵同步 WorldQuant Brain 所有模擬設定、資料集與欄位，並自動存檔。

- `set_telegram_chat_id.py`  
  取得 Telegram chat id 並寫入 `.env`，方便機器人推播。

- `pyproject.toml`、`uv.lock`  
  Python 環境與依賴管理，建議使用 `uv` 工具。

- `README.md`、`doc/README_zh.md`  
  安裝、環境設置與使用說明（中英文）。

---

### 主要資料夾

- `src/`  
  專案核心程式碼，包含：
  - `worker.py`：主控模擬流程與 Telegram 機器人互動。
  - `generation_algorithm.py`：遺傳演算法主體。
  - `experiment_analysis.py`：實驗結果分析與圖表產生。
  - `alphalist.py`、`alpha.py`：Alpha 個體與清單的定義與操作。
  - `data_sync.py`：資料同步工具。
  - 其他輔助模組（如 `scorer.py`, `paths.py` 等）。

- `DB/`
  - `ResearchDB/`：研究設定、模板、GA 參數、執行過程與資料。
    - `template/`：模板 JSON（如 `default_template.json`）。
    - `setting/`：payload 設定 JSON。
    - `ga_config/`：遺傳演算法參數 JSON。
    - `research_process/`、`data/`：執行過程與同步資料（執行時產生）。
  - `AlphaDB/`：Alpha 任務狀態資料夾。
    - `pending/`、`running/`、`complete/`、`error/`、`pnl/`：分別存放不同狀態的 alpha 任務與結果。

- `doc/`  
  文件資料夾，包含：
  - `CLOUD_INFRA.md`：雲端部署說明。
  - `EXPERIMENT_ANALYSIS_README.md`：分析模組詳細說明。
  - `README_zh.md`：中文安裝與操作說明。
  - `images/`：說明用圖片。

---

## 典型流程

1. **環境設置**：依 `README.md` 或 `doc/README_zh.md` 步驟安裝 Python 環境與依賴。
2. **設定帳號與 Telegram**：依說明設置 `.env`，並執行 `set_telegram_chat_id.py`。
3. **啟動主程式**：執行 `main_code.py` 或 `sample_script.py` 開始模擬與研究。
4. **分析與結果**：分析結果與圖表會自動儲存於 `DB/ResearchDB/research_process/<研究名稱>/analysis/`。

bonus:
1. **資料同步**：執行 `run_data_sync.py` 下載最新模擬設定與資料。

---

## 其他說明

- **資料夾與檔案命名**皆依功能分類，便於維護與擴充。
- **敏感資料**（如帳號密碼）請勿提交至版本控制，建議使用 `.env` 管理。
- **詳細參數與進階用法**請參考 `doc/` 內相關說明文件。

---

如需更詳細的模組或流程說明，請參考 `doc/` 目錄下的文件或 `README.md`。 