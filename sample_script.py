#!/usr/bin/env python3
"""
遺傳算法 Alpha 研究腳本
支援同時優化基因池變數和設置參數

使用方法：
1. 固定設置模式（向後兼容）：
   python sample_script.py -s "GLB_MINVOL1M_1_STATISTICAL.json"
   
2. 設置參數優化模式（新功能）：
   python sample_script.py -s "GLB_MINVOL1M_1_OPT.json"
   
設置檔案格式：
- 固定值：直接指定值，如 "delay": 1
- 可優化參數：使用陣列，如 "decay": [0, 2, 4, 6, 8]

系統會自動偵測設置檔案格式並選擇適當的運行模式。
"""

import json
import os
import argparse

from src.paths import ResearchPath
from src.scorer import SharpeScore, FitnessScore, TurnoverScore, AbsSharpeScore, PiecewiseSharpeScore, PiecewiseTurnoverScore, CombinedPiecewiseScore
from src.generation_algorithm import GenerationAlgorithm
from src.experiment_analysis import analyze_experiment_results

parser = argparse.ArgumentParser(description="Run genetic algorithm for alpha research (supports both gene pool and setting optimization).")
parser.add_argument("-r", "--research_name", default="real_test", help="Name of the research process")
parser.add_argument("-t", "--template_file", default="default_template.json", help="Template JSON file in template directory")
parser.add_argument("-s", "--setting_file", default="default_setting.json", help="Setting JSON file in setting directory (supports both fixed values and optimization arrays)")
parser.add_argument("-g", "--ga_config_file", default="default_ga_config.json", help="GA config JSON file in ga_config directory")
parser.add_argument("--auto_run", action="store_true", help="Auto run without confirmation dialog")

args = parser.parse_args()

# Load template and gene_pool from JSON
with open(os.path.join(ResearchPath.TEMPLATE, args.template_file), 'r') as f:
    template_data = json.load(f)
    template = template_data["template"]
    gene = template_data["gene_pool"]

print(f"✓ 已載入模板: {args.template_file}")
print(f"  - 基因池包含 {len(gene)} 個變數: {list(gene.keys())}")

# Load settings from JSON
with open(os.path.join(ResearchPath.SETTING, args.setting_file), 'r') as f:
    setting_data = json.load(f)

# 檢查是否有可優化的設置參數
optimizable_params = [key for key, value in setting_data.items() if isinstance(value, list)]
fixed_params = [key for key, value in setting_data.items() if not isinstance(value, list)]

print(f"✓ 已載入設置檔案: {args.setting_file}")
print(f"  - 固定參數: {len(fixed_params)} 個")
if optimizable_params:
    print(f"  - 可優化參數: {len(optimizable_params)} 個")
    for param in optimizable_params:
        print(f"    * {param}: {len(setting_data[param])} 個選項")
    print("  ✓ 啟用設置參數優化模式")
    setting_optimization_data = setting_data
    # 在設置優化模式下，payload 不包含 settings，會在 GA 中動態生成
    payload = {
        'type': 'REGULAR',
        'regular': None,
    }
else:
    print("  ✓ 使用固定設置模式（向後兼容）")
    setting_optimization_data = None
    # 在固定設置模式下，payload 包含固定的 settings
    payload = {
        'type': 'REGULAR',
        'regular': None,
        'settings': setting_data
    }

# 評估模板複雜度 （顯示所有基因選項排列組合的數量）
gene_pool_keys = list(gene.keys())
gene_pool_values = [gene[key] for key in gene_pool_keys]
gene_pool_combinations = 1
for values in gene_pool_values:
    gene_pool_combinations *= len(values)
print(f"✓ 基因池包含 {len(gene)} 個變數: {list(gene.keys())}")
print(f"  - 基因池包含 {gene_pool_combinations} 種排列組合")


# Load ga_config from JSON
with open(os.path.join(ResearchPath.GA_CONFIG, args.ga_config_file), 'r') as f:
    ga_config = json.load(f)

print(f"✓ 已載入 GA 配置: {args.ga_config_file}")
print(f"  - 世代數: {ga_config['generation_num']}")
print(f"  - 族群大小: {ga_config['kid_num']}")
print(f"  - 父母數量: {ga_config['parent_num']}")

research_name = args.research_name

score_scorer = CombinedPiecewiseScore
additional_scorer = [FitnessScore, PiecewiseSharpeScore, PiecewiseTurnoverScore]

print("\n" + "=" * 70)
print(f"初始化遺傳算法 - 研究名稱: {research_name}")
print("=" * 70)

# 創建 GenerationAlgorithm 實例，支援設置參數優化
ga = GenerationAlgorithm(gene_pool=gene,
                          payload=payload,
                          template=template,
                          main_scorer=score_scorer,
                          addition_scorer=additional_scorer,
                          research_name=research_name,
                          ga_config=ga_config,
                          setting_data=setting_optimization_data)  # 新增參數

print("✓ 遺傳算法初始化完成")
print("  - 將同時優化基因池變數和設置參數" if setting_optimization_data else "  - 使用固定設置模式")

print("\n" + "=" * 70)
print("開始運行遺傳算法...")
print("=" * 70)

ga.run(auto_run=args.auto_run)

# ============== 實驗結果分析 ==============
print("\n" + "=" * 70)
print("遺傳算法運行完成！開始進行實驗結果分析...")
print("=" * 70)

# 執行完整的實驗分析（包含世代分數演化、基因多樣性、設置多樣性、代表性AlphaList創建）
analysis_summary = analyze_experiment_results(research_name, corr_threshold=0.7)

print("\n" + "=" * 70)
print("實驗與分析完成！")
print(f"✓ 結果保存在: DB/ResearchDB/research_process/{research_name}/")
if setting_optimization_data:
    print("✓ 已同時優化基因池變數和設置參數")
    print("✓ 設置參數多樣性分析已包含在結果中")
print("=" * 70)

print("\n實驗分析完成！總結如下：")
print("-" * 50)
print(f"實驗名稱: {analysis_summary['experiment_name']}")
print(f"總世代數: {analysis_summary['total_generations']}")

if analysis_summary['score_evolution_summary']['initial_fitness_mean'] is not None:
    print(f"初始平均 Fitness: {analysis_summary['score_evolution_summary']['initial_fitness_mean']:.6f}")
    print(f"最終平均 Fitness: {analysis_summary['score_evolution_summary']['final_fitness_mean']:.6f}")
    print(f"Fitness 改善幅度: {analysis_summary['score_evolution_summary']['fitness_improvement']:.6f}")
    print(f"最高 Fitness: {analysis_summary['score_evolution_summary']['max_fitness_achieved']:.6f}")

print(f"代表性 Alpha 數量: {analysis_summary['representative_alphas']['total_count']}")
if analysis_summary['representative_alphas']['top_fitness_scores']:
    print(f"前5名 Fitness 分數: {[f'{score:.6f}' for score in analysis_summary['representative_alphas']['top_fitness_scores']]}")

print(f"\n✓ 所有分析結果已保存到: DB/ResearchDB/research_process/{research_name}/analysis/")
print("  - generation_score_evolution.png: 世代分數演化圖")
print("  - genetic_diversity.png: 基因多樣性分析圖")
if setting_optimization_data:
    print("  - setting_diversity.png: 設置參數多樣性分析圖")
print("  - generation_score_evolution.csv: 世代分數演化數據")
print("  - genetic_diversity.csv: 基因多樣性數據")
if setting_optimization_data:
    print("  - setting_diversity.csv: 設置參數多樣性數據")
print("  - experiment_summary.json: 實驗總結報告")
print("  - experiment_representative_alphalist: 代表性AlphaList")
print("=" * 70)
